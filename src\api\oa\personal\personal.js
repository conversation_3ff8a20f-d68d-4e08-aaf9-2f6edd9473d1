import request from '@/utils/request'

/** 待办
 * 待处理
 * */
// 流程任务列表(接口公用：type 1待办 2待阅读 3被退回 4已办)
export function getWorkTaskList(query) {
  return request({
    url: `/process/task`,
    method: 'get',
    params: query
  })
}

// 根据单个id提交流程
export function putWorkTask(id) {
  return request({
    url: `/process/submit/busId/${id}`,
    method: 'PUT',
  })
}

// 根据单个id审批
export function putWorkTaskApprove(query) {
  return request({
    url: `/process/adopt/busId`,
    method: 'PUT',
    data: query
  })
}
// 批量id审批  只审批审批中的任务
export function batchApprove(query) {
  return request({
    url: `/process/adopt/busIds`,
    method: 'PUT',
    data: query
  })
}
// 日报智能带出
export function automaticUserInfo() {
  return request({
    url: `/project/dailyRecord/automaticUserInfo`,
    method: 'get'
  })
}

// 批量id审批
export function putWorkTasksApprove(ids) {
  return request({
    url: `/process/handle/busId`,
    method: 'PUT',
    data: ids
  })
}

// 拒绝单个id审批
export function rejectWorkTask(query) {
  return request({
    url: `/process/refuse/busId`,
    method: 'PUT',
    data: query
  })
}

// 撤销
export function revocationWorkTask(id) {
  return request({
    url: `/process/revoke/busId/${id}`,
    method: 'post',
  })
}

// 转发
export function transmitWorkTask(query) {
  return request({
    url: `/process/relay/busId`,
    method: 'post',
    data: query
  })
}

// 转办
export function turnToWorkTask(query) {
  return request({
    url: `/process/transfer/busId`,
    method: 'post',
    data: query
  })
}

// 根据id获取流程轨迹
export function flowPathWorkTask(id) {
  return request({
    url: `/process/track/busId/${id}`,
    method: 'get'
  })
}

// 根据id获取已完成节点
export function getReturnBackList(id) {
  return request({
    url: `/process/compleAct/busId/${id}`,
    method: 'get'
  })
}

// 退回到某个节点
export function returnBackWorkTask(query) {
  return request({
    url: `/process/sendBack/busId`,
    method: 'post',
    data: query
  })
}

// 根据业务id提交到退回节点
export function putReturnBackWorkTask(query) {
  return request({
    url: `/process/submitSendBack/busId`,
    method: 'PUT',
    data: query
  })
}

/** 待办
 * 待阅读
 * */
export function putWaitingRead(id) {
  return request({
    url: `/process/relay/busId/${id}/read`,
    method: 'PUT',
  })
}

/** 待办
 * 全部已读
 * */
export function putAllRead() {
  return request({
    url: `/process/relay/allRead`,
    method: 'PUT',
  })
}


/** 已办 */
// 获取业务类别
export function getDefKey(category) {
  return request({
    url: `/process/deploy/select`,
    method: 'get',
    params: {category: category}
  })
}

/**
 * 日报
 * */
// 根据年月查询当月的日历
export function getDateList(query) {
  return request({
    url: `/project/dailyRecord/getCalendarList`,
    method: 'get',
    params: query,
  })
}

// 根据年月日项目id查询日报详情
export function getDateDailyReport(query) {
  return request({
    url: `/project/dailyReport/getDate`,
    method: 'post',
    data: query,
  })
}

// 查询日报列表
export function getDailyReportList(data) {
  return request({
    url: `/project/dailyRecord/seeDaily`,
    method: 'post',
    data: data,
  })
}

// 获取项目列表接口
export function getProjectList(data) {
  return request({
    url: `/project/projectInfo/list`,
    method: 'post',
    data
  })
}

// 获取常用地区
export function getAreaList(query) {
  return request({
    url: `/system/areaCommon/list`,
    method: 'post',
    data: query
  })
}

// 提交日报
export function temporaryOrSubmitDaily(query) {
  return request({
    url: `/project/dailyRecord/temporaryOrSubmitDaily`,
    method: 'post',
    data: query
  })
}
// 查看日报详情
export function getDailyReportDetail(id) {
  return request({
    url: `/project/dailyRecord/queryDailyOne/${id}`,
    method: 'get',
  })
}
// 暂存日报
export function putDailyReport(query) {
  return request({
    url: `/project/dailyRecord/temporaryDaily`,
    method: 'post',
    data: query
  })
}

/**
 * 工时
 * */
// 根据年月获取周列表
export function getWeekList(query) {
  return request({
    url: `/project/workingHour/${query.year}/${query.month}`,
    method: 'get',
  })
}

// 新增工时
export function postWorkingHour(query) {
  return request({
    url: `/project/workingHour/saveBatchDto`,
    method: 'post',
    data: query
  })
}

// 查看工时详情
export function getWorkingHourDetail(id) {
  return request({
    url: `/project/workingHour/${id}`,
    method: 'get',
  })
}

// 获取工时类型
export function getProjectWorkingHour() {
  return request({
    url: `/project/common/getProjectWorkingHourRecordEnum`,
    method: 'get',
  })
}

//代办查看流程图
export function seeDeployWorkFlowImg(id) {
  return request({
    url: `/process/image/busId/${id}`,
    method: 'get'
  })
}
// 更新
export function updateProcess(id) {
  return request({
    url: `/process/${id}/upVersion`,
    method: 'PUT'
  })
}
// 全量更新
export function updateAllProcess(id) {
  return request({
    url: `/process/upVersion`,
    method: 'PUT'
  })
}
// 系统审批
// 作废
export function deleteCancellation(id) {
  return request({
    url: `/process/cancel/busId/${id}`,
    method: 'DELETE'
  })
}
// 干预
export function putIntervene(formDto) {
  return request({
    url: `/process/intervene/busId`,
    method: 'PUT',
    data: formDto
  })
}
// 获取节点列表
export function getNodes(busId) {
  return request({
    url: `/process/nodes/${busId}`,
    method: 'get',
  })
}
//加签
export function countersign(query) {
  return request({
    url: `/process/countersign/busId`,
    method: 'post',
    data: query
  })
}
// 审批通过回调
export function successCallback(busId) {
  return request({
    url: `/process/busIds/${busId}/successCallback`,
    method: 'PUT',
  })
}
// 删除
export function deleteProcess(busId) {
  return request({
    url: `/process/cancel/busId/${busId}`,
    method: 'DELETE'
  })
}

//我的出勤
export function dailyRecord(query) {
  return request({
    url: `/project/dailyRecord/myAttendance`,
    method: 'post',
    data: query
  })
}

//批量审批
export function batchApproval(query) {
  return request({
    url: `/process/batchApproval`,
    method: 'put',
    data: query
  })
}



// 导出
export function exportMyAttendance(query){
  return request({
    url: `/project/dailyRecord/exportMyAttendance`,
    method: 'POST',
    data: query,
    responseType: 'blob',
  })
}

//系统审批  批量审批
export function batchOperation(data) {
  return request({
    url: `/process/batchOperation`,
    method: 'put',
    data
  })
}

export function forbiddenTermination(data) {
  return request({
    url: `/process/termination`,
    method: 'put',
    data
  })
}
//获取某人签字
export function queryUserSignature(data) {
  return request({
    url: `/system/user/queryUserSignature`,
    method: 'get',
    data
  })
}
