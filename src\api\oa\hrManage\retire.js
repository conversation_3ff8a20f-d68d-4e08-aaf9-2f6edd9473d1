// 退休人员
import request from '@/utils/request'

// 员工退休-查询可退休姓名
export function getDetail(data) {
  return request({
    url: `/system/workInfo/listUsers`,
    method: 'post',
    data
  })
}

// 选择退休员工查询信息
export function getUserInfo(id) {
  return request({
    url: `/system/workInfo/tranAndTurnByUserId/${id}`,
    method: 'get',
  })
}

// 查询退休人员信息（通过ID）
export function getRetireInfo(id) {
  return request({
    url: `/hr/retireRecord/${id}`,
    method: 'get',
  })
}

// 新增退休人员
export function addRecord(data) {
  return request({
    url: `/hr/retireRecord`,
    method: 'post',
    data
  })
}

// 修改退休人员
export function updataRecord(data) {
  return request({
    url: `/hr/retireRecord/updateRetireRecord`,
    method: 'post',
    data
  })
}

// 查询退休人员信息列表
export function getList(data) {
  return request({
    url: `/hr/retireRecord/getList`,
    method: 'post',
    data
  })
}