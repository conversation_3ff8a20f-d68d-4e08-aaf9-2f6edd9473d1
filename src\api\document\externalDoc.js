import request from '@/utils/request'

// 分页查询外部收文列表
export function listExternalDoc(query) {
  return request({
    url: '/document/external/list',
    method: 'post',
    data: query
  })
}

// 保存外部收文
export function addExternalDoc(data) {
  return request({
    url: '/document/external',
    method: 'post',
    data: data
  })
}

// 修改外部收文
export function updateExternalDoc(data) {
  return request({
    url: '/document/external',
    method: 'put',
    data: data
  })
}

// 删除外部收文
export function delExternalDoc(id) {
  return request({
    url: '/document/external/' + id,
    method: 'delete'
  })
}

// 查询外部收文详细
export function getExternalDoc(id) {
  return request({
    url: '/document/external/' + id,
    method: 'get'
  })
}

//传阅
export function circulateExternalDoc(data) {
  return request({
    url: '/document/external/circulate',
    method: 'post',
    data: data
  })
}

