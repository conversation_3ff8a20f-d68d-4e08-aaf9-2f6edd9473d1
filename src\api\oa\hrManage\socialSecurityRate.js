import request from '@/utils/request'

// 获取页面列表数据
export function getSocial (query) {
  return request({
    url: `/hr/salaryRateConfig/bili-list`,
    method: 'get',
    params: query
  })
}

// 获取社保比列枚举字典值数据
export function getSalaryRateConfigTypeEnumMap () {
  return request({
    url: `/hr/common/getSalaryRateConfigTypeEnumMap`,
    method: 'get'
  })
}

// 修改五险一金比例
export function putSocialRate (data) {
  return request({
    url: `/hr/salaryRateConfig/insurance-rate`,
    method: 'put',
    params: data
  })
}

// 获取社保缴纳比例列表  员工
export function socialList (data) {
  return request({
    url: `/hr/salaryRateConfig/list`,
    method: 'post',
    data: data
  })
}


// 获取社保缴纳比例详情
export function socialListDetail (id) {
  return request({
    url: `/hr/salaryRateConfig/${id}`,
    method: 'get',
  })
}
// 获取社保缴纳比例详情 员工信息
export function socialListDetailTop (id) {
  return request({
    url: `/hr/roster/${id}`,
    method: 'get',
  })
}
// 修改  社保构成
export function editSocialList (query) {
  return request({
    url: `/hr/salaryRateConfig`,
    method: 'put',
    data: query
  })
}
// 下载员工社保
export function downSocialList (query) {
  return request({
    url: `/hr/salaryRateConfig/exportRateConfigs`,
    method: 'get',
    params: query,
    responseType: "blob"
  })
}

// 上传员工社保
export function exportSocialList (query) {
  return request({
    url: `/hr/salaryRateConfig/importRateConfigs`,
    method: 'POST',
    data: query,
  })
}


