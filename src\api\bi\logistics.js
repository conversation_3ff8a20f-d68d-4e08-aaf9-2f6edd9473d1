import request from '@/utils/request'
// 智能后勤

//库存管理
export function screenShedList(){
  return request({
    url: `/data/logisticsScreen/screenShedList`,
    method: 'get',
  })
}

//车辆管理
export function vehicleInfoList(){
  return request({
    url: `/data/logisticsScreen/vehicleInfoList`,
    method: 'get',
  })
}

//维修管理
export function maintenance(){
  return request({
    url: `/data/logisticsScreen/maintenance`,
    method: 'get',
  })
}

//入库
export function screenShedEnterList(){
  return request({
    url: `/data/logisticsScreen/screenShedEnterList`,
    method: 'get',
  })
}

//出库
export function screenShedLeaveList(){
  return request({
    url: `/data/logisticsScreen/screenShedLeaveList`,
    method: 'get',
  })
}

//进销
export function screenShedAccessList(){
  return request({
    url: `/data/logisticsScreen/screenShedAccessList`,
    method: 'get',
  })
}

//车辆调度
export function vehicleDispatchList(){
  return request({
    url: `/data/logisticsScreen/vehicleDispatchList`,
    method: 'get',
  })
}

//车况概况
export function vehicleInfo(){
  return request({
    url: `/data/logisticsScreen/vehicleInfo`,
    method: 'get',
  })
}