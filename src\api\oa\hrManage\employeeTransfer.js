import request from '@/utils/request'

// 查询员工
export function getDirectManager(data) {
  return request({
    url: '/system/workInfo/listUsers',
    method: 'post',
    data: data
  })
}

// 根据userId查询个人信息
export function getPersonalData(id) {
  return request({
    url: `hr/transfer/tranAndTurn/${id}`,
    method: 'get'
  })
}

//提交人员基本信息列表
export function putEmployeeTransfer(query) {
  return request({
    url: '/hr/transfer',
    method: 'post',
    data: query
  })
}

// 查询调动人员详情
export function getEmployeeTransferDetail(id) {
  return request({
    url: `/hr/transfer/tranAndTurnByid/${id}`,
    method: 'get',
  })
}
// ------------------------新增调转人员功能列表----------------------- 

// 新增调动员工列表
export function getEmployeeList(data) {
  return request({
    url: `/hr/hrAppoint/userTransferRecordList`,
    method: 'post',
    data:data
  })
}
// 调动详情
export function getTransferDetail(data) {
  return request({
    url: `/hr/hrAppoint/` + data,
    method: 'get',
  })
}

// 调动详情 -- 待办
export function getTransferDetailId(data) {
  return request({
    url: `/hr/transfer/tranAndTurnByid/` + data,
    method: 'get',
  })
}