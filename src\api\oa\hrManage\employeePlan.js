import request from '@/utils/request'

// 查询招聘计划列表
export function listEmployeePlan(query) {
  return request({
    url: '/oa/employeePlan/list',
    method: 'get',
    params: query
  })
}

// 查询招聘计划详细
export function getEmployeePlan(employeePlanId) {
  return request({
    url: '/oa/employeePlan/' + employeePlanId,
    method: 'get'
  })
}

// 新增招聘计划
export function addEmployeePlan(data) {
  return request({
    url: '/oa/employeePlan',
    method: 'post',
    data: data
  })
}

// 修改招聘计划
export function updateEmployeePlan(data) {
  return request({
    url: '/oa/employeePlan',
    method: 'put',
    data: data
  })
}

// 删除招聘计划
export function delEmployeePlan(employeePlanId) {
  return request({
    url: '/oa/employeePlan/' + employeePlanId,
    method: 'delete'
  })
}
