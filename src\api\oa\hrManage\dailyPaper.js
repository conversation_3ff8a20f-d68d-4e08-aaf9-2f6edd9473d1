import request from '@/utils/request'

// 查询薪资列表
export function salaryCountRecord(query) {
  return request({
    url: 'hr/salaryCountRecord/rate-from',
    method: 'get',
    params:query
  })
}
// 查询roster列表
export function listRosterUser(data) {
  return request({
      url: '/hr/roster/basesList',
      method: 'post',
      data: data
  })
}
//导出薪资报表
export function getExport(query) {
  return request({
      url: '/hr/salaryCountRecord/rate-from/export',
      method: 'get',
      params: query
  })
}
// 获取期间
export function getNewDate() {
  return request({
    url: 'hr/periodManagement/yearmonth',
    method: 'get'
  })
}
// 查询期间列表
export function getNewDateList() {
  return request({
    url: 'hr/periodManagement/list',
    method: 'get'
  })
}
//部门树
export function getTreeSelect(){
return request({
  url: '/hr/roster/treeselect',
    method: 'get',

})
}
//下载
export function download(data){
  return request({
    url: '/hr/salaryCountRecord/rate-from/export',
    method: 'post',
    responseType: 'blob',
    data:data
  })
}
