import request from '@/utils/request'

// 进行查看来访登记列表
export function getVisitorList(data) {
  return request({
    url: `/administration/adVisitorRegistration/list`,
    method: 'post',
    data: data
  });
}

// 进行查看来访登记
export function getVisitorInfo(id) {
  return request({
    url: `/administration/adVisitorRegistration/${id}`,
    method: 'get',
  });
}

// 进行提交/暂存来访登记
export function adVisitorRegistration(data) {
  return request({
    url: `/administration/adVisitorRegistration`,
    method: 'post',
    data: data
  });
}

// 进行提交/暂存来访登记
export function downloadTemplate() {
  return request({
    url: `/administration/adVisitorRegistration/download/template`,
    method: 'get',
    responseType: 'blob',
  });
}