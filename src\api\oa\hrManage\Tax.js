import request from '@/utils/request'

// 查询报税列表
export function getTaxList(query) {
  return request({
    url: 'hr/taxReturn/list',
    method: 'get',
    params: query
  })
}

// 导出报税列表
export function postTaxReturn(query) {
  return request({
    url: 'hr/taxReturn/exportTax',
    method: 'get',
    params:query,
    responseType: 'blob',
  })
}
// 导出报税列表 前置条件
export function checkExportTax(query) {
  return request({
    url: '/hr/taxReturn/checkExportTax',
    method: 'get',
    params:query,
  })
}

// 上传个税

export function postImportTax(data) {
  return request({
    url: 'hr/taxReturn/importTax',
    method: 'post',
    data: data,
    headers:{ 'Content-Type': 'multipart/form-data' }
  })
}
