import request from '@/utils/request'

// 查询合同业务类型
export function agreementTypeList(data) {
    return request({
      url: '/project/contractType/list',
      method: 'post',
      data
    })
  }

  // 新增
export function addAgreementType(data) {
    return request({
      url: `/project/contractType`,
      method: 'post',
      data
    })
  }

  // 修改
export function update(data) {
    return request({
      url: `/project/contractType/update`,
      method: 'put',
      data
    })
  }

  // 删除
export function dalete(id) {
    return request({
      url: `/project/contractType/${id}`,
      method: 'delete',
    })
  }

