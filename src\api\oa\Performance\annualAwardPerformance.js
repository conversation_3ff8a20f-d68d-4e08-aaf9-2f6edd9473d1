import request from '@/utils/request'

// 进行提交/暂存年度奖励性绩效
export function performanceYearReward(data) {
	return request({
		url: `/hr/performanceYearReward`,
		method: 'post',
		data
	});
}

// 进行查询奖励性绩效
export function getPerformanceYearReward(id) {
	return request({
		url: `/hr/performanceYearReward/${id}`,
		method: 'get',
	});
}

// 进行查询年度奖励性绩效列表
export function getperformanceList(data) {
	return request({
		url: `/hr/performanceYearReward/list`,
		method: 'post',
		data
	});
}

// 进行查询年度奖励性绩效列表
export function performanceImport(data) {
	return request({
		url: `/hr/performanceYearRewardDetail/exportPerformanceYearReward`,
		responseType: 'blob',
		method: 'post',
		data
	});
}
