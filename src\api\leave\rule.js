import request from '@/utils/request'

// 分页查询机关代字列表
export function listType(query) {
  return request({
    url: '/attendance/leaveType/list',
    method: 'post',
    data: query
  })
}

// 查询机关代字详细
export function getType(id) {
  return request({
    url: '/attendance/leaveType/' + id,
    method: 'get'
  })
}

// 修改机关代字
export function updateType(data) {
  return request({
    url: '/attendance/leaveType',
    method: 'put',
    data: data
  })
}

export function listLeaveType(){
  return request({
    url: '/attendance/leaveType/optionList',
    method: 'post'
  })
}

export function getSuplusByUserId(id) {
  return request({
    url: '/attendance/leaveType/getSuplusByUserId/' + id,
    method: 'get'
  })
}

export function addBalanceUser(query) {
  return request({
    url: '/attendance/leaveBalance',
    method: 'post',
    data: query
  })
}

// 分页查询机关代字列表
export function listBalance(query) {
  return request({
    url: '/attendance/leaveBalance/list',
    method: 'post',
    data: query
  })
}

// 查询机关代字详细
export function getBalanceById(id) {
  return request({
    url: '/attendance/leaveBalance/' + id,
    method: 'get'
  })
}


