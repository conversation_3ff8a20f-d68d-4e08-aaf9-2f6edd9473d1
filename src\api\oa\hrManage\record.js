import request from '@/utils/request'

// 查询合同列表
export function listRecord(data) {
    return request({
        url: '/system/contractInfo/list',
        method: 'post',
        data
    })
}


// 查询合同列表
export function userListRecord(data) {
    return request({
        url: '/system/contractInfo/userList',
        method: 'post',
        data
    })
}

// 新增合同
export function addRecord(data) {
    return request({
        url: '/system/contractInfo',
        method: 'post',
        data: data
    })
}
// 修改合同
export function updateRecord(data) {
    return request({
        url: '/system/contractInfo',
        method: 'put',
        data: data
    })
}

// 修改rss关键词
export function updateRss(data) {
    return request({
        url: '/oa/notice/Keyword',
        method: 'put',
        data: data
    })
}

// 查询首页合同列表
export function indexListRecord(query) {
    return request({
        url: '/oa/notice/indexList',
        method: 'get',
        params: query
    })
}

export function getRecordType() {
    return request({
        url: '/oa/notice/getRecordType',
        method: 'get',
    })
}

// 修改合同的红点显示
export function updateBadgeRecord(id) {
    return request({
        url: '/oa/notice/editBadge/' + id,
        method: 'put'
    })
}



// 删除合同
export function delRecord(id) {
    return request({
        url: '/oa/notice/' + id,
        method: 'delete'
    })
}

/////////////////变更接触

//查看合同详情
export function recordDetail(data) {
    return request({
        url: '/system/contractInfo/getUserContract/' + data,
        method: 'get',
    })
}

//查看单个合同
export function recordDetails(data) {
    return request({
        url: '/system/contractInfo/' + data,
        method: 'get',
    })
}

//解除合同
export function recordSecure(data) {
    return request({
        url: '/system/contractInfo/endContract/',
        method: 'put',
        data
    })
}

//变更合同
export function recordChange(data) {
    return request({
        url: '/system/contractInfo/change/',
        method: 'post',
        data
    })
}