import request from '@/utils/request'

// 下达任务列表
export function projectList(query) {
  return request({
    url: '/fee/budProjectInfo/list',
    method: 'post',
    data: query
  })
}

// 查询顶级单位
export function topList(id) {
    return request({
        url: '/system/organizational/getTopParentList',
        method: 'get'
    })
}

// 查询项目详情
export function projectDetail(id) {
    return request({
        url: '/fee/budProjectInfo/' + id,
        method: 'get'
    })
}

// 新增项目
export function addProject(data) {
    return request({
        url: '/fee/budProjectInfo/',
        method: 'post',
        data
    })
}

// 修改项目
export function updateProject(data) {
    return request({
        url: '/fee/budProjectInfo',
        method: 'put',
        data
    })
}


// 新增支出登记
export function projectPayOut(data) {
  return request({
      url: '/fee/budProjectSubtaskPay/',
      method: 'post',
      data
  })
}

// 支出登记 下拉子任务列表
export function getTaskList(data) {
  return request({
      url: '/fee/budProjectSubtask/list',
      method: 'post',
      data
  })
}

// 支出登记 预算合计详情
export function projectInfo(data) {
  return request({
      url: '/fee/budProjectInfo/fee/' + data,
      method: 'get',
  })
}

// 代办 支出登记详情
export function payDetail(data) {
  return request({
      url: '/fee/budProjectSubtaskPay/' + data,
      method: 'get',
  })
}
