import request from '@/utils/request'

// 电脑类型
export function computerTypeList(data) {
  return request({
    url: `/system/computerConfigure/computerTypeList`,
    method: 'post',
    data: data
  });
}
// 配置要求
export function computerConfigure(data) {
  return request({
    url: `/system/computerConfigure/list`,
    method: 'post',
    data: data
  });
}

// 配置要求
export function computerRentalInfo(data) {
  return request({
    url: `/administration/computerRentalInfo/saveOne`,
    method: 'post',
    data: data
  });
}

// 详情
export function administration(id) {
  return request({
    url: `/administration/computerRentalInfo/${id}`,
    method: 'get',
  });
}

// 修改费用
export function computerRentalDetail(data) {
  return request({
    url: `/administration/computerRentalDetail/updateByList`,
    method: 'POST',
    data:data
  });
}
