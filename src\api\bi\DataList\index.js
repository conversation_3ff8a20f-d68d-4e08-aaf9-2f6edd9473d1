import request from "@/utils/request";
// 部门经营管理指标
// 1、项目交付率  2、客户满意度   3、项目支撑率   4、人员空载率     5、新业务拓展
//获取项目交付率
export function getDeliveryRateList(localDate) {
  return request({
    url: `/data/managementOverview/deliveryRateList?year=${localDate}`,
    method: "get",
  });
}
//获取客户满意度
export function getSatisfactionList(localDate) {
  return request({
    url: `/data/managementOverview/satisfactionList?year=${localDate}`,
    method: "get",
  });
}
//获取项目支撑率
export function getSupportRateList(localDate) {
  return request({
    url: `/data/managementOverview/supportRateList?year=${localDate}`,
    method: "get",
  });
}
//获取人员空载率
export function getUnloadRateList(localDate) {
  return request({
    url: `/data/managementOverview/unloadRateList?year=${localDate}`,
    method: "get",
  });
}
//获取新业务拓展
export function getDevelopmentList(localDate) {
  return request({
    url: `/data/managementOverview/developmentList?year=${localDate}`,
    method: "get",
  });
}

// 公司项目概览 经营管理指标看板
// /projectInfo/list
//获取公司项目概览
export function getOverview(localDate,orgId,businessLineId) {
  return request({
    url: `/data/projectOverview/detail?localDate=${localDate}&orgId=${orgId}&businessLineId=${businessLineId}`,
    method: "get",
  });
}

// 部门经营指标看板
export function getManagementKanBan(localDate,orgId,businessLineId) {
  return request({
    url: `/data/orgBoard/getOrgBoard?localDate=${localDate}&orgId=${orgId}&businessLineId=${businessLineId}`,
    method: "get",
  });
}
// 部门经营指标看板 穿透列表
export function getManagementList(data) {
  return request({
    url: `/data/orgBoard/getOrgBoard`,
    method: "post",
    data: data,
  });
}

// 根据省份id获取项目信息列表
export function getByProvinceId(data) {
  return request({
    url: `/data/projectOverview/getByProvinceId`,
    method: "post",
    data: data,
  });
}
// 项目概览左侧列表
export function getByLeft(data) {
  return request({
    url: `/data/projectOverview/leftPenetrationList`,
    method: "post",
    data: data,
  });
}
// 项目概览右侧列表
export function getByRight(data) {
  return request({
    url: `/data/projectOverview/rightPenetrationList`,
    method: "post",
    data: data,
  });
}
// 项目概览成本列表
export function getByCost(data) {
  return request({
    url: `/data/projectOverview/costDetail`,
    method: "post",
    data: data,
  });
}

//获取字典数据  项目类型
export function getProjectType() {
  return request({
    url: `/system/data/type/project_type_enum`,
    method: "get",
  });
}
//获取项目等级
export function getProjectLevelEnum() {
  return request({
    url: `/project/common/getProjectLevelEnum`,
    method: "get",
  });
}
//获取项目完成状态
export function getProjectFinishedStatusEnum() {
  return request({
    url: `/project/common/getProjectFinishedStatusEnum`,
    method: "get",
  });
}
// 获取事业部门/url: `/project/common/getOrgList?type=4`,
// export function getBusinessDepartment() {
//   return request({
//     url: `/project/common/getOrgList`,
//     method: "get",
//   });
// }

export function getBusinessDepartment() {
  return request({
    url: `/system/organizational/list`,
    method: "post",
    data:{}
  });
}

//人员成本支出成本
export function selectProjectPersonCost(data) {
  return request({
    url: `/data/projectOverview/selectProjectPersonCost`,
    method: "post",
    data: data,
  });
}


//导出Excel
export function exportExcel(url,data){
  return request({
    url: url,
    method: 'post',
    responseType: 'blob',
    data:data
  })
}
