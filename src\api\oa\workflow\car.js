import request from '@/utils/request'

// 查询用车申请列表
export function listCar(query) {
  return request({
    url: '/oa/workflow/car/list',
    method: 'get',
    params: query
  })
}

// 查询用车申请历史列表
export function historyCar(query) {
  return request({
    url: '/oa/workflow/car/history',
    method: 'get',
    params: query
  })
}

// 查询用车申请详细
export function getCar(id) {
  return request({
    url: '/oa/workflow/car/detail/' + id,
    method: 'get'
  })
}

// 新增用车申请
export function addCar(data) {
  return request({
    url: '/oa/workflow/car',
    method: 'post',
    data: data
  })
}
// 修改用车申请
export function editCar(data) {
  return request({
    url: '/oa/workflow/car',
    method: 'put',
    data: data
  })
}

// // 修改用车申请
// export function updateCar(data) {
//   return request({
//     url: '/oa/workflow/car',
//     method: 'put',
//     data: data
//   })
// }
//
// // 删除用车申请
// export function delCar(id) {
//   return request({
//     url: '/oa/workflow/car/' + id,
//     method: 'delete'
//   })
// }
