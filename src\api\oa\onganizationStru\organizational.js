import request from '@/utils/request'

// 查询组织架构列表
export function listOrganizational(query) {
    return request({
        url: '/hr/roster/orgList',
        method: 'post',
        data: query
    })
}

// 查询组织架构详细
export function getOrganizational(id) {
    return request({
        url: 'system/organizational/' + id,
        method: 'get'
    })
}

// 新增组织架构
export function addOrganizational(data) {
    return request({
        url: 'system/organizational',
        method: 'post',
        data: data
    })
}

// 修改组织架构
export function updateOrganizational(data) {
    return request({
        url: 'system/organizational',
        method: 'put',
        data: data
    })
}

// 删除组织架构
export function delOrganizational(id) {
    return request({
        url: 'system/organizational/' + id,
        method: 'delete'
    })
}
