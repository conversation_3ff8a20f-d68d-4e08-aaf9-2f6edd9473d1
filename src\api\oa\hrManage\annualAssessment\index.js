import request from '@/utils/request'

// 综合查询
// 进行综合查询
export function getList(data) {
  return request({
    url: `/hr/performanceYearExamineDetail/getUserList`,
    method: 'post',
    data
  })
}

// 进行查看列表详情
export function getInfo(data) {
  return request({
    url: `/hr/performanceYearExamineDetail/getPerformanceYearExamineDetailDto`,
    method: 'post',
    data
  })
}

// 进行修改公开状态
export function updateIsOpen(data) {
  return request({
    url: `/hr/performanceYearExamineDetail/updateIsOpen`,
    method: 'post',
    data
  })
}

// 进行导出综合查询
export function download(data) {
  return request({
    url: `/hr/performanceYearExamineDetail/exportGetUserList`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}

// 历年查询
// 进行年度考核历年查询
export function getYearList(data) {
  return request({
    url: `/hr/performanceYearExamineDetail/getOverUserList`,
    method: 'post',
    data
  })
}
// 进行导出年度考核历年查询
export function downloadYear(data) {
  return request({
    url: `/hr/performanceYearExamineDetail/exportGetOverUserList`,
    method: 'post',
    data,
    responseType: 'blob',
  })
}
// 进行综合查询
export function getOverUserList(data) {
  return request({
    url: `/hr/performanceYearExamineDetail/getOverUserListId`,
    method: 'post',
    data
  })
}
