import request from '@/utils/request'

// 进行查询工资汇总表信息
export function getSalaryList(data) {
  return request({
    url: '/hr/hrSalaryMonth/list/'+data,
    method: 'get',
  })
}

// 进行工资汇总表-生成
export function setSalaryGenerate(data) {
  return request({
    url: '/hr/hrSalaryMonth/generate',
    method: 'post',
    data
  })
}

// 进行工资汇总表-导出
export function exportSalaryMonth(data) {
  return request({
    url: '/hr/hrSalaryMonth/exportSalaryMonth',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 通过type类型进行查询节点信息
export function getMonthType(data) {
  return request({
    url: '/hr/hrSalaryMonth/getMonthType',
    method: 'post',
    data
  })
}

// 进行工资汇总表-核对
export function getCheckMonth(data) {
  return request({
    url: '/hr/hrSalaryMonth/getCheckMonth',
    method: 'post',
    data
  })
}

// 进行工资汇总表-确认
export function getConfirm(data) {
  return request({
    url: '/hr/hrSalaryMonth/getConfirm',
    method: 'post',
    data
  })
}

// 进行工资汇总表-锁定
export function getLock(data) {
  return request({
    url: '/hr/hrSalaryMonth/getLock',
    method: 'post',
    data
  })
}

// 查看
// 进行查询工资详情列表
export function getMonthDetail(data) {
  return request({
    url: '/hr/hrSalaryMonth/getMonthDetail',
    method: 'post',
    data
  })
}

// 进行发送工资表
export function hrSalaryMonthSendUser(data) {
  return request({
    url: '/hr/hrSalaryMonthSendUser',
    method: 'post',
    data
  })
}

// 进行工资汇总表-导出
export function exportGetMonthDetail(data) {
  return request({
    url: '/hr/hrSalaryMonth/exportGetMonthDetail',
    method: 'post',
    responseType: 'blob',
    data
  })
}
