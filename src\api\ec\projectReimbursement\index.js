import request from '@/utils/request'

// 获取备用金剩余金额
export function getLockerAmount (projectId) {
  return request({
    url: '/fee/projectImprest/lockerAmountsByProjectId/'+projectId,
    method: 'get',
  })
}

export function getLockerAmountByUserId (userId) {
  return request({
    url: '/fee/projectImprest/lockerAmounts/'+userId,
    method: 'get',
  })
}

// 提交报销申请
export function postReimbursement (query) {
  return request({
    url: `/fee/projectReimbursement`,
    method: 'post',
    data: query
  })
}
// 修改报销申请
export function putReimbursement (query) {
  return request({
    url: `/fee/projectReimbursement`,
    method: 'put',
    data: query
  })
}

// 获取交报销申请详情
export function getReimbursementDetail (id) {
  return request({
    url: `/fee/projectReimbursement/info-dto/${id}`,
    method: 'get',
  })
}

// 根据当前登陆人userId获取银行信息
export function getBankInfo (id) {
  return request({
    url: `/system/bankCard/getBankInfoByUserId/${id}`,
    method: 'get',
  })
}
// 校验【差旅费-出差补助；差旅费-住宿费】规则
export function checkProjectReimbursement (data) {
  return request({
    url: `/fee/projectReimbursement/checkRules`,
    method: 'post',
    data: data
  })
}
// 根据type查询采购报销科目列表
export function reimbursementType (data) {
  return request({
    url: `/fee/reimbursementType/list`,
    method: 'post',
    data: data
  })
}
// 新增or修改报销申请
export function postSaveOrUpdateData (data) {
  return request({
    url: `/fee/reimbursement/saveOrUpdateData`,
    method: 'post',
    data: data
  })
}
// 新增or修改付款申请
export function paySaveOrUpdateData (data) {
  return request({
    url: `/fee/payment/saveOrUpdateData`,
    method: 'post',
    data: data
  })
}
// 获取报销申请详细信息
export function reimbursementDetail (id) {
  return request({
    url: `/fee/reimbursement/detail/${id}`,
    method: 'get',
  })
}
// 获取报销申请详细信息
export function getDetailTravel(id) {
  return request({
    url: `/fee/reimbursement/detailTravel/${id}`,
    method: 'get',
  })
}
// 获取交通工具枚举
export function getTraffic () {
  return request({
    url: `/fee/reimbursement/getTraffic`,
    method: 'get',
  })
}
// 获取市内交通工具枚举
export function getTrafficCity () {
  return request({
    url: `/fee/reimbursement/getTrafficCity`,
    method: 'get',
  })
}
// 根据科目获取行政采购申请列表
export function selectListByType (data) {
  return request({
    url: `/administration/purchaseInfo/selectListByType`,
    method: 'post',
    data: data
  })
}
// 查询电脑租赁申请明细列表
export function computerRentalDetail (data) {
  return request({
    url: `/administration/computerRentalDetail/list`,
    method: 'post',
    data: data
  })
}
// 导入快递费用
export function importCourierFees (data) {
  return request({
    url: `/fee/payment/importCourierFees`,
    method: 'post',
    data: data
  })
}
// 获取付款申请详细信息
export function paymentDetail (id) {
  return request({
    url: `/fee/payment/detail/${id}`,
    method: 'get',
  })
}
//预览费用报销单：
export function viewFee(id) {
	return request({
		url: `fee/reimbursement/apply/fee/${id}`,
		method: 'get'
	});
}
//导出费用报销单：
export function viewFeeExport(id) {
  return request({
    url: `fee/reimbursement/apply/fee/export/${id}`,
    method: 'get'
  });
}
// 预览市内交通报销单：
export function viewTraffic(id) {
	return request({
		url: `fee/reimbursement/apply/traffic/${id}`,
		method: 'get'
	});
}
// 导出市内交通报销单：
export function viewTrafficExport(id) {
  return request({
    url: `fee/reimbursement/apply/traffic/export/${id}`,
    method: 'get'
  });
}
//预览差旅费用报销单：
export function viewTravel(id) {
	return request({
		url: `fee/reimbursement/apply/travel/${id}`,
		method: 'get'
	});
}
//导出差旅费用报销单：
export function viewTravelExport(id) {
  return request({
    url: `fee/reimbursement/apply/travel/export/${id}`,
    method: 'get'
  });
}
//预览付款申请报销单：
export function viewPayment(id) {
	return request({
		url: `fee/payment/preview/payment/${id}`,
		method: 'get'
	});
}
//导出付款申请报销单：
export function viewPaymentExport(id) {
  return request({
    url: `fee/payment/preview/payment/export/${id}`,
    method: 'get'
  });
}
// 付款申请中删除电脑租赁
export function deleteComputer(data) {
  return request({
    url: '/administration/computerRentalDetail/updateById',
    method: 'post',
    data: data
  })
}
//获取城市
export function getCity() {
	return request({
		url: `fee/reimbursement/getCity`,
		method: 'get'
	});
}
export function contractPaymentExport(id) {
  return request({
    url: `/fee/payment/preview/contractPayment/export/`+id,
    method: 'get'
  });
}
