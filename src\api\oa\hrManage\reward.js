import request from '@/utils/request'

// 查询奖惩列表 -- 汇总
export function listRewardInfo(data) {
    return request({
        url: '/hr/hrEncouragePunishInfo/list',
        method: 'post',
        data
    })
}
// 查询奖惩列表
export function listReward(data) {
    return request({
        url: '/hr/hrEncouragePunish/list',
        method: 'post',
        data
    })
}

// 新增奖惩 -- 作废
export function addReward(data) {
    return request({
        url: '/hr/hrEncouragePunish',
        method: 'post',
        data: data
    })
}
// 修改奖惩 -- 作废
// export function updateReward(data) {
//     return request({
//         url: '/hr/hrEncouragePunish',
//         method: 'put',
//         data: data
//     })
// }

// 新增/修改奖惩
export function updateReward(data) {
    return request({
        url: '/hr/hrEncouragePunishInfo',
        method: 'post',
        data: data
    })
}
// 删除奖惩
export function delReward(id) {
    return request({
        url: '/hr/hrEncouragePunish/' + id,
        method: 'delete'
    })
}
// 删除奖惩
export function delRewardInfo(id) {
    return request({
        url: '/hr/hrEncouragePunishInfo/' + id,
        method: 'delete'
    })
}

// 奖惩详情
export function detailReward(id) {
    return request({
        url: '/hr/hrEncouragePunishInfo/' + id,
        method: 'get'
    })
}