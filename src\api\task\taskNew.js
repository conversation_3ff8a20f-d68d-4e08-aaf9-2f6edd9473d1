import request from '@/utils/request'

// 任务列表
export function listTask(query) {
    return request({
        url: '/work/tkTaskInfo/list',
        method: 'post',
        data: query
    })
}

// 任务列表 已结项
export function listTaskType(query) {
    return request({
        url: '/work/tkTaskInfo/endList',
        method: 'post',
        data: query
    })
}
// 任务列表 已结项
export function endPlanList(query) {
    return request({
        url: '/work/tkTaskInfo/endPlanList',
        method: 'post',
        data: query
    })
}
// 任务新增
export function addTask(query) {
    return request({
        url: '/work/tkTaskInfo',
        method: 'post',
        data: query
    })
}
//新增工作计划
export function addWork(data) {
    return request({
        url: '/work/tkTaskInfo',
        method: 'post',
        data: data
    })
}
//工作计划列表
export function planList(data) {
    return request({
        url: '/work/tkTaskInfo/planList',
        method: 'post',
        data: data
    })
}
//周任务列表
export function weekList(data) {
    return request({
        url: '/work/tkTaskInfo/weekList',
        method: 'post',
        data: data
    })
}

// 任务分解
export function breakTask(query) {
    return request({
        url: '/work/tkTaskInfo/detailed',
        method: 'post',
        data: query
    })
}

// 任务分解
export function breakTaskNew(query) {
    return request({
        url: '/work/tkTaskInfo/newDetailed',
        method: 'post',
        data: query
    })
}

// 任务安排 任务详情
export function detailTask(data) {
    return request({
        url: '/work/tkTaskInfo/' + data,
        method: 'get',
    })
}

// 任务安排 任务驳回详情
export function detailRejectTask(data) {
    return request({
        url: '/work/tkReject/getRejectInfo/' + data,
        method: 'get',
    })
}

//  任务反馈  任务详情
export function feedDetail(data) {
    return request({
        url: '/work/tkFeedback/' + data,
        method: 'get',
    })
}

// 任务安排 反馈列表
export function feedList(data) {
    return request({
        url: '/work/tkFeedback/list/',
        method: 'post',
        data
    })
}

// 执行反馈
export function feedBack(data) {
    return request({
        url: '/work/tkFeedback',
        method: 'post',
        data
    })
}
// 执行反馈 工作计划
export function feedBackNew(data) {
    return request({
        url: '/work/tkFeedback/newAdd',
        method: 'post',
        data
    })
}

// 任务安排 拓扑图
export function topologyList(data) {
    return request({
        url: '/work/tkTaskInfo/topology/' + data,
        method: 'get'
    })
}
// 任务安排 拓扑图 工作计划
export function topologyListNew(data) {
    return request({
        url: '/work/tkTaskInfo/newTopology/' + data,
        method: 'get'
    })
}

// 任务安排 任务结项
export function endTask(data) {
    return request({
        url: '/work/tkTaskInfo/end/' + data,
        method: 'get'
    })
}

// 任务安排 任务驳回
export function rejectTask(data) {
    return request({
        url: '/work/tkReject/',
        method: 'post',
        data
    })
}

// 任务安排 任务驳回 工作计划
export function rejectTaskNew(data) {
    return request({
        url: '/work/tkReject/newAdd',
        method: 'post',
        data
    })
}

// 任务列表 已读未读
export function readDocumentList(data) {
    return request({
        url: '/work/tkUserRecord/'+  data,
        method: 'get',
    })
}

// 任务列表 点击任务安排 调取已读接口
export function readTask(data) {
    return request({
        url: '/work/tkTaskInfo/userRecord',
        method: 'put',
        data
    })
}
// export function readTask(data) {
//     return request({
//         url: '/work/tkUserRecord',
//         method: 'put',
//         data
//     })
// }

// 任务分解 删除已分解的任务时 判断是否已经再次分解
export function judgeTask(data) {
    return request({
        url: '/work/tkTaskInfo/check/' + data,
        method: 'get',
    })
}

// 任务分解 删除任务
export function deleteTask(data) {
    return request({
        url: '/work/tkTaskInfo/' + data,
        method: 'delete',
    })
}