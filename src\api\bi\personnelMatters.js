import request from '@/utils/request'
// 智能人事

//本月入职
export function joinedThisMonth(){
  return request({
    url: `/data/personnelScreen/joinedThisMonth`,
    method: 'get',
  })
}

//职员人数趋势
export function numberTrend(){
  return request({
    url: `/data/personnelScreen/numberTrend`,
    method: 'get',
  })
}

//薪酬统计
export function salaryStatistics(){
  return request({
    url: `/data/personnelScreen/salaryStatistics`,
    method: 'get',
  })
}

//学历分布
export function sysEducations(){
  return request({
    url: `/data/personnelScreen/sysEducations`,
    method: 'get',
  })
}

//年龄分布
export function queryAges(){
  return request({
    url: `/data/personnelScreen/queryAges`,
    method: 'get',
  })
}

//员工类型分布
export function queryStaffType(){
  return request({
    url: `/data/personnelScreen/queryStaffType`,
    method: 'get',
  })
}

//教职/非教职分布
export function teachingPosition(){
  return request({
    url: `/data/personnelScreen/teachingPosition`,
    method: 'get',
  })
}

//奖励性绩效分布
export function monthlyBasicsSummary(){
  return request({
    url: `/data/personnelScreen/monthlyBasicsSummary`,
    method: 'get',
  })
}

//员工关怀
export function employeeCares(){
  return request({
    url: `/data/personnelScreen/employeeCares`,
    method: 'get',
  })
}

//岗位培训
export function hrCultivateList(){
  return request({
    url: `/data/personnelScreen/hrCultivateList`,
    method: 'get',
  })
}

//出勤
export function attendance(){
  return request({
    url: `/data/personnelScreen/attendance`,
    method: 'get',
  })
}