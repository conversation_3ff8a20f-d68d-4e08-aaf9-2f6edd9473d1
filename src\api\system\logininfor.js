import request from '@/utils/request'
import data from "../../views/system/dict/data"

// 查询登录日志列表
export function list (data) {
  return request({
    url: '/system/logininfor/list',
    method: 'post',
    data
  })
}

// 删除登录日志
export function delLogininfor (infoId) {
  return request({
    url: '/system/logininfor/' + infoId,
    method: 'delete'
  })
}

// 清空登录日志
export function cleanLogininfor () {
  return request({
    url: '/system/logininfor/clean',
    method: 'delete'
  })
}

//忘记密码
export function forgetPassword (userName, password, phonenumber, code, uuid) {
  return request({
    url: '/system/logininfor/forgetPassword',
    method: 'post',
    data: { userName, password, phonenumber, code, uuid }
  })
}
