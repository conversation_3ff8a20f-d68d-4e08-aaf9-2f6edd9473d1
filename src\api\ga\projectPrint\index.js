import request from '@/utils/request'
// 新增项目用印申请
export function postProjectSeal(data) {
	return request({
		url: `/administration/projectSeal`,
		method: 'POST',
    data:data
	});
}
// 修改项目用印申请
export function putProjectSeal(data) {
	return request({
		url: '/administration/projectSeal',
		method: 'PUT',
    data:data
	});
}
// 获取项目用印申请详情
export function getProjectSeal(id) {
	return request({
    url: `/administration/projectSeal/${id}`,
		method: 'GET',
	});
}
// 获取项目用印申请列表
export function postProjectSealList(data) {
	return request({
		url: '/administration/projectSeal/list',
		method: 'POST',
    data:data
	});
}
