import request from "@/utils/request";

// 获取项目阶段列表
export function projectTypeMilepostList(data) {
  return request({
    url: "/project/projectTypeMilepost/list",
    method: "get",
    params: data,
  });
}

// 获取项目类型列表
export function getListByType(data) {
  return request({
    url: "/project/projectTypeMilepost/getListByType",
    method: "post",
    data: data,
  });
}

// 删除项目类型列表
export function removeById(path) {
  return request({
    url: path,
    method: "delete",
  });
}

//保存项目类型
export function saveOrUpdateBatch(data) {
  return request({
    url: "/project/projectTypeMilepost/saveOrUpdateBatch",
    method: "post",
    data: data,
  });
}

//新增项目阶段
export function addProjectTypeMilepost(data) {
  return request({
    url: "/project/projectTypeMilepost",
    method: "post",
    params: data,
  });
}

// 删除项目阶段列表
export function removeByType(data) {
  return request({
    url: "/project/projectTypeMilepost/removeByType",
    method: "delete",
    params: data,
  });
}
