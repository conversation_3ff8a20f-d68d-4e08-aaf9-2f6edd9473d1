import request from '@/utils/request'

// 查询flyback人员列表
export function getPageFly (data) {
  return request({
    url: '/hr/flyBack/pageFly',
    method: 'post',
    data
  })
}
// 添加or修改flyback人员
export function saveOrUpdateFly(data) {
  return request({
    url: '/hr/flyBack/saveOrUpdateFly',
    method: 'post',
    data
  })
}
// 删除flyback人员
export function deleteFly(userId) {
  return request({
    url: `/hr/flyBack/deleteFly/${userId}`,
    method: 'delete',
  })
}
export function getFlyback(userId) {
  return request({
    url: '/hr/flyBack/list/'+userId,
    method: 'get'
  })
}