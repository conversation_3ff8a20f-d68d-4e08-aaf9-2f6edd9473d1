import request from '@/utils/request'

// 分页查询机关代字列表
export function listOrganCode(data) {
  return request({
    url: '/document/organCode/list',
    method: 'post',
    data
  })
}

// select机关代字列表
export function allOrganCode(query) {
  return request({
    url: '/document/organCode/all',
    method: 'get',
    params: query
  })
}

// 新增机关代字
export function addOrganCode(data) {
  return request({
    url: '/document/organCode',
    method: 'post',
    data: data
  })
}

// 修改机关代字
export function updateOrganCode(data) {
  return request({
    url: '/document/organCode',
    method: 'put',
    data: data
  })
}

// 删除机关代字
export function delOrganCode(id) {
  return request({
    url: '/document/organCode/' + id,
    method: 'delete'
  })
}

// 查询机关代字详细
export function getOrganCode(id) {
  return request({
    url: '/document/organCode/' + id,
    method: 'get'
  })
}


