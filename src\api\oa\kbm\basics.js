import request from '@/utils/request'

// 查询知识库列表
export function listBasics(query) {
  return request({
    url: '/oa/kbmBasics/list',
    method: 'get',
    params: query
  })
}

// 查询知识库详细
export function getBasics(id) {
  return request({
    url: '/oa/kbmBasics/' + id,
    method: 'get'
  })
}

// 新增知识库
export function addBasics(data) {
  return request({
    url: '/oa/kbmBasics',
    method: 'post',
    data: data
  })
}

// 修改知识库
export function updateBasics(data) {
  return request({
    url: '/oa/kbmBasics',
    method: 'put',
    data: data
  })
}

// 删除知识库
export function delBasics(id) {
  return request({
    url: '/oa/kbmBasics/' + id,
    method: 'delete'
  })
}
