import request from "@/utils/request";

export function upload(isChange, file) {
  return request({
    url: "/hr/roster/uploadFile",
    method: "post",
    data: { isChange, file },
  });
}

export function basicFormSub(data) {
  return request({
    url: "hr/roster/updateEmployeeBase",
    method: "put",
    data: data,
  });
}
export function workFormSub(data) {
  return request({
    url: "hr/roster/updateWorkInfo",
    method: "put",
    data: data,
  });
}
export function familyFormSub(data) {
  return request({
    url: "hr/roster/updateFamilyInfo",
    method: "put",
    data: data,
  });
}
export function contrFormSub(data) {
  return request({
    url: "hr/roster/updateContractInfo",
    method: "put",
    data: data,
  });
}
export function persFormSub(data) {
  return request({
    url: "hr/roster/updateUser",
    method: "put",
    data: data,
  });
}
export function bankFormSub(data) {
  return request({
    url: "hr/roster/updateBankCard",
    method: "put",
    data: data,
  });
}
export function emgFormSub(data) {
  return request({
    url: "hr/roster/updateEmergencyContract",
    method: "put",
    data: data,
  });
}
export function eduFormSub(data) {
  return request({
    url: "hr/roster/updateEducationInfo",
    method: "put",
    data: data,
  });
}
//base address and accumulation/social security address
export function usuallyAddress() {
  return request({
    url: "hr/roster/areaList",
    method: "get",
  });
}
// contract company
export function contractCompany() {
  return request({
    url: "hr/roster/regList",
    method: "get",
  });
}
// 获取个人详情信息
export function getRosterUser(id) {
  return request({
    url: `hr/roster/${id}`,
    method: "get",
  });
}

// 获取职级
export function getZhiji(query) {
  return request({
    url: `/system/rank/list`,
    method: "post",
    data: query,
  });
}

//中电运行分公司
export function registerCompany(query) {
  return request({
    url: "/system/registerCompany/list",
    method: "post",
    data: query,
  });
}

//物理删除当前期间的所有数据
export function deletePeriodList(query) {
  return request({
    url: `hr/salaryRateConfig/deletePeriodList/${query}`,
    method: "delete",
  });
}
