import request from '@/utils/request'

// 分页查询考勤组列表
export function listClasses(query) {
  return request({
    url: '/attendance/dutyClasses/list',
    method: 'get',
    params: query
  })
}

// 提交考勤组
export function addClasses(data) {
  return request({
    url: '/attendance/dutyClasses',
    method: 'post',
    data: data
  })
}

// 修改考勤组
export function updateClasses(data) {
  return request({
    url: '/attendance/dutyClasses',
    method: 'put',
    data: data
  })
}

// 删除考勤组
export function delClasses(id) {
  return request({
    url: '/attendance/dutyClasses/' + id,
    method: 'delete'
  })
}

// 查询考勤组详细
export function getClasses(id) {
  return request({
    url: '/attendance/dutyClasses/' + id,
    method: 'get'
  })
}


