import request from '@/utils/request'

// 分页查询公文模板列表
export function listCustomer(query) {
  return request({
    url: '/manuscript/msCustomer/list',
    method: 'post',
    data: query
  })
}

// 新增公文模板
export function addCustomer(data) {
  return request({
    url: '/manuscript/msCustomer',
    method: 'post',
    data: data
  })
}

// 修改公文模板
export function editCustomer(data) {
  return request({
    url: '/manuscript/msCustomer',
    method: 'put',
    data: data
  })
}

// 删除公文模板
export function delCustomer(id) {
  return request({
    url: '/manuscript/msCustomer/' + id,
    method: 'delete'
  })
}

// 查询公文模板详细
export function getCustomer(id) {
  return request({
    url: '/manuscript/msCustomer/' + id,
    method: 'get'
  })
}


