<template>
  <div class="app-container">
    <div class="publicBackColor">
      <TopTitle :title="title" @back="back" />
      <el-form ref="queryParams" :model="queryParams" label-width="130px" :rules="rules" :disabled="formDisabled"
        class="autoShowData">
        <Subtitle title="项目信息" />
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="申请人：" prop="createByName">
              <el-input v-model="queryParams.createByName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请部门：" prop="orgName">
              <el-input v-model="queryParams.orgName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请时间：" prop="nowTime">
              <el-input v-model="createTime1" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <Subtitle title="报销信息" />
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="报销金额合计：" prop="totalFee">
              <el-input class="moneyStyle" v-model="queryParams.totalFee" disabled>
                <span slot="append">元</span>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发票张数：" prop="invoice">
              <el-input class="moneyStyle" v-model="queryParams.invoice"
                oninput="value=value.replace(/^0(0+)|[^\d]+/g,'')" disabled>
                <span slot="append">张</span>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="付款主体公司：" prop="companyId">
              <el-select size="small" v-model="queryParams.companyId" clearable filterable placeholder="请选择">
                <el-option v-for="item in orgList" :key="item.id" :label="item.orgName" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="领款人：" prop="collection">
              <el-radio-group v-model="plate">
                <el-radio label="1">平台用户</el-radio>
                <el-radio label="2">非平台用户</el-radio>
              </el-radio-group>
              <el-input v-if="plate === '2'" v-model="queryParams.collection" placeholder="请输入领款人" maxlength="20" />
              <UserByDeptSelect v-if="plate === '1'" :initSelection="initMainDeliverySelection" :nickName="queryParams.collection"
                @selectName="selectCopyDelivery" />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="收款账户：" prop="payeeAccount">
              <el-input v-model="queryParams.payeeAccount" @input="inputPayeeAccount($event)" maxlength="30" />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="账户开户行：" prop="openBank">
              <el-input v-model="queryParams.openBank" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <Subtitle title="报销明细" />
        <div class="applyList" v-for="(applyItem, applyIndex) in queryParams.applyList" :key="applyIndex">
          <div class="applyTitleBox">
            <div class="applyTitle">
              <span>差旅交通段</span>
              <div class="cursor" @click="hideShowClick(applyIndex)"><i
                  :class="applyItem.hide == true ? 'el-icon-s-unfold-rotate el-icon-s-unfold' : 'el-icon-s-unfold'"></i>
              </div>
              <div class="applySection">
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="出发城市：" :prop="'applyList.' + applyIndex + '.startAddrId'" label-width="90px"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'change',
                      }">
                      <el-cascader ref="cascaderStart" v-model="applyItem.startAddrId" clearable :options="cityOptions"
                        :props="{
                          expandTrigger: 'hover',
                          value: 'id',
                          label: 'areaName',
                          checkStrictly: true,
                          emitPath: false
                        }" filterable @visible-change="cityVisible($event, applyIndex, 'start')"
                        @change="cityChange($event, applyIndex, 'start')">
                        <template slot-scope="{ node, data }">
                          <span style="display: inline-block;width:100%" @click="cascaderClick(data, 'cascaderStart')">{{
                            data && data.areaName }}</span>
                        </template>
                      </el-cascader>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="到达城市：" :prop="'applyList.' + applyIndex + '.endAddrId'" :rules="{
                      required: true,
                      message: '请选择',
                      trigger: 'change',
                    }">
                      <el-cascader ref="cascaderEnd" v-model="applyItem.endAddrId" clearable :options="cityOptions"
                        :props="{
                          expandTrigger: 'hover',
                          value: 'id',
                          label: 'areaName',
                          checkStrictly: true,
                          emitPath: false
                        }" filterable @visible-change="cityVisible($event, applyIndex, 'end')"
                        @change="cityChange($event, applyIndex, 'end')">
                        <template slot-scope="{ node, data }">
                          <span style="display: inline-block;width:100%" @click="cascaderClick(data, 'cascaderEnd')">{{
                            data && data.areaName }}</span>
                        </template>
                      </el-cascader>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
              <span style="margin-left: 100px;">报销金额总计：{{ (price[applyIndex].sum).toFixed(2) }}元，票据张数总计：{{
                bill[applyIndex].sum
              }}张</span>
            </div>
            <div>
              <div style="margin-right: 12px; display:inline-block;">
                <el-button type="text" v-for="item in buttons" :key="item.type" @click="isShow(item.type, applyIndex)">{{
                  item.name }}</el-button>
              </div>
              <el-button type="text" @click="deleteApplyItem(applyIndex)">
                <img src="@/assets/icons/deleteIcon.png" alt="" style="height: 18px; width: 18px;">
              </el-button>

            </div>
          </div>
          <div :class="applyItem.hide == true ? 'applyItemHide' : 'applyItem applyItem' + applyIndex">
            <div class="_table" v-if="applyItem.trafficList.length
              ">
              <div class="padding20">报销科目：差旅-交通费<span style="margin-left: 100px;">报销金额小计：{{
                price[applyIndex].trafficList.fee }}元，票据张数小计：{{ bill[applyIndex].trafficList.bill }}张</span>
              </div>
              <el-table :data="applyItem.trafficList" border style="width: 100%">
                <el-table-column label="报销科目" width="166">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.typeId" placeholder="请选择">
                      <template v-for="item in optionTypeList">
                        <el-option v-if="item.type == 1001 && item.sign == 2" :key="item.id" :label="item.name"
                          :value="item.id" @click.native="
                            chooseType(
                              item.type,
                              item.sign,
                              scope,
                              'trafficList',
                              item.must,
                              item.state,
                              applyIndex
                            )
                            ">
                        </el-option>
                      </template>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="交通工具" width="176">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.trafficList.' +
                      scope.$index +
                      '.traffic'
                      " :rules="{
    required: scope.row.typeId ? true : false,
    message: '请选择',
    trigger: 'blur',
  }">
                      <el-select v-model="scope.row.traffic" clearable placeholder="请选择" :disabled="!scope.row.typeId">
                        <template v-if="scope.row.typeId === '19'">
                          <el-option v-for="item in trafficList" :key="item.value" :label="item.label"
                            :value="item.value">
                          </el-option>
                        </template>
                        <template v-if="scope.row.typeId === '21'">
                          <el-option v-for="item in trafficCityList" :key="item.value" :label="item.label"
                            :value="item.value">
                          </el-option>
                        </template>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="乘车日期" width="200">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.trafficList.' + scope.$index + '.trafficDate'" :rules="{
    required: scope.row.typeId ? true : false,
    message: '请选择',
    trigger: 'blur',
  }">
                      <el-date-picker style="width: 90%" v-model="scope.row.trafficDate" type="date"
                        value-format="yyyy-MM-dd" placeholder="请选择" :disabled="!scope.row.typeId" :picker-options="pickerOptions(
                          scope.row,
                          'trafficList',
                          scope.row.trafficDate,
                          scope.$index,
                          'startDate',
                          applyIndex
                        )
                          ">
                      </el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="到达日期" width="200">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.trafficList.' + scope.$index + '.endDate'" :rules="{
    required: scope.row.typeId ? true : false,
    message: '请选择',
    trigger: 'blur',
  }">
                      <el-date-picker style="width: 90%" v-model="scope.row.endDate" type="date"
                        :disabled="!scope.row.typeId" value-format="yyyy-MM-dd" placeholder="请选择" :picker-options="pickerOptions(
                          scope.row,
                          'trafficList',
                          scope.row.endDate,
                          scope.$index,
                          'endDate',
                          applyIndex
                        )
                          " @change="changeTime($event, scope, 'trafficList', applyIndex)">
                      </el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="出发地" width="166">
                  <template slot-scope="scope">
                    <el-form-item label="">
                      <el-input v-model="scope.row.startAddr" placeholder="出发地" maxlength="30" show-word-limit></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="目的地" width="166">
                  <template slot-scope="scope">
                    <el-form-item>
                      <el-input v-model="scope.row.endAddr" placeholder="目的地" maxlength="30" show-word-limit></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="报销金额" width="166">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.trafficList.' + scope.$index + '.fee'" :rules="{
    required: scope.row.typeId ? true : false,
    message: '请输入',
    trigger: 'blur',
  }">
                      <el-input v-model="scope.row.fee" placeholder="请输入" :disabled="!scope.row.typeId" maxlength="10"
                        @input="commonPrice($event, scope, 'trafficList', applyIndex)"
                        @change="sumFee('trafficList', applyIndex)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="发票张数" width="150px">
                  <template slot-scope="scope">
                    <el-form-item label=" "
                      :prop="'applyList.' + applyIndex + '.trafficList.' + scope.$index + '.invoice'" :rules="{
                        required: !!scope.row.fee,
                        message: '请输入',
                        trigger: 'blur',
                      }">
                      <el-input v-model="scope.row.invoice" placeholder="请输入" :disabled="!scope.row.typeId"
                        @input="commonInvoice($event, scope.row)" @change="sumBill('trafficList', applyIndex)"
                        maxlength="3"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="附件" width="120">
                  <template slot-scope="scope">
                    <el-upload class="upload" :data="{ type: 33 }" :action="uploadFileUrl" :headers="headers"
                      :file-list="scope.row.fileList"
                      :on-success="(e, file, fileList) => {
                        return uploadSuccess(e, file, fileList, scope, 1)}"
                      :class="{ disUoloadSty: scope.row.noneBtn }"
                      v-if="scope.row.fileList.length == 0"
                      :limit="1"
                      :before-upload="beforeUpload">
									<el-button type="button" class="ant-btn">
										<span>文件上传</span>
									</el-button>
								</el-upload>
								<div v-else>
									<span style="cursor: pointer; color:blue;font-size:12px;margin-right:10px"
									@click="showUserDocumentFile(scope.row)">下载</span>
									<span style="cursor: pointer; color:blue;font-size:12px;margin-right:10px"
									@click="handlePreview(scope.row)">预览</span>
									<span style="cursor: pointer; color:blue;font-size:12px" v-if="!formDisabled" @click="uploadRemove(scope.row)" >删除</span>
								</div>
                  </template>
                </el-table-column>
                <el-table-column label="备注" width="200">
                  <template slot-scope="scope">
                    <el-input maxlength="200" :disabled="!scope.row.typeId" v-model="scope.row.remark" placeholder="请输入">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80px" fixed="right">
                  <template slot-scope="scope">
                    <el-button circle icon="el-icon-plus" @click="add('trafficList', scope.$index, applyIndex)"
                      class="noHover">
                    </el-button>
                    <el-button circle icon="el-icon-minus" @click="pop('trafficList', scope.$index, applyIndex)"
                      class="noHover">
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="_table" v-if="applyItem.stayList.length">
              <div class="padding20">报销科目：差旅-住宿费<span style="margin-left: 100px;">报销金额小计：{{
                (price[applyIndex].stayList.fee).toFixed(2) }}元，票据张数小计：{{ bill[applyIndex].stayList.bill }}张</span>
              </div>
              <el-table :data="applyItem.stayList" border style="width: 100%;">
                <el-table-column label="报销科目" width="166">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.typeId" placeholder="请选择">
                      <template v-for="item in optionTypeList">
                        <el-option v-if="item.type == 1001 && item.sign == 1" :key="item.id" :label="item.name"
                          :value="item.id" @click.native="
                            chooseType(
                              item.type,
                              item.sign,
                              scope,
                              'stayList',
                              item.must,
                              item.state,
                              applyIndex
                            )
                            ">
                        </el-option>
                      </template>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="开始日期" width="200">
                  <template slot-scope="scope">
                    <el-date-picker style="width: 90%" v-model="scope.row.startDate" type="date" @change="
                      checkProjectReimbursement($event, scope, 'stayList', applyIndex)" value-format="yyyy-MM-dd"
                      :picker-options="pickerOptions(
                        scope.row,
                        'stayList',
                        scope.row.startDate,
                        scope.$index,
                        'startDate',
                        applyIndex
                      )
                        " placeholder="请选择" :disabled="!scope.row.typeId">
                    </el-date-picker>
                  </template>
                </el-table-column>
                <el-table-column label="结束日期" width="200">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.stayList.' + scope.$index + '.endDate'" :rules="{
    required: scope.row.startDate ? true : false,
    message: '请选择',
    trigger: 'change',
  }">
                      <el-date-picker style="width: 90%" v-model="scope.row.endDate" type="date" :picker-options="pickerOptions(
                        scope.row,
                        'stayList',
                        scope.row.endDate,
                        scope.$index,
                        'endDate',
                        applyIndex
                      )
                        " :disabled="scope.row.startDate ? false : true" value-format="yyyy-MM-dd" placeholder="请选择"
                        @change="
                          checkProjectReimbursement($event, scope, 'stayList', applyIndex)
                          ">
                      </el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="出差地" width="166">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.stayList.' + scope.$index + '.endAddr'" :rules="{
    required: scope.row.startDate ? true : false,
    message: '请输入',
    trigger: 'blur',
  }">
                      <el-input v-model="scope.row.endAddr" placeholder="请输入" maxlength="30" show-word-limit>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="天数" width="80">
                  <template slot-scope="scope">
                    <span>{{ scope.row.days }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="房间单价(元)" width="166">
                  <template slot-scope="scope">
                    <span>{{ scope.row.roomFee }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="报销金额(元)" width="166">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.stayList.' + scope.$index + '.fee'" :rules="{
    required: scope.row.startDate ? true : false,
    message: '请输入',
    trigger: 'blur',
  }">
                      <el-input :disabled="scope.row.days ? false : true" v-model="scope.row.fee" maxlength="10"
                        @input="commonPrice($event, scope, 'stayList', applyIndex)"
                        @change="sumFee('stayList', applyIndex)" placeholder="请输入"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="发票张数" width="150px">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' + applyIndex + '.stayList.' + scope.$index + '.invoice'"
                      :rules="{
                        required: !!scope.row.fee,
                        message: '请输入',
                        trigger: 'blur',
                      }">
                      <el-input v-model="scope.row.invoice" @input="commonInvoice($event, scope.row)" placeholder="请输入"
                        @change="sumBill('stayList', applyIndex)" maxlength="3"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="附件" width="120">
                  <template slot-scope="scope">
                    <el-upload class="upload" :data="{ type: 33 }" :action="uploadFileUrl" :headers="headers"
                      :file-list="scope.row.fileList"
                      :on-success="(e, file, fileList) => {
                        return uploadSuccess(e, file, fileList, scope, 1)}"
                      :class="{ disUoloadSty: scope.row.noneBtn }"
                      v-if="scope.row.fileList.length == 0"
                      :limit="1"
                      :before-upload="beforeUpload">
                      <el-button type="button" class="ant-btn">
                        <span>文件上传</span>
                      </el-button>
                    </el-upload>
                    <div v-else>
                      <span style="cursor: pointer; color:blue;font-size:12px;margin-right:10px"
                      @click="showUserDocumentFile(scope.row)">下载</span>
                      <span style="cursor: pointer; color:blue;font-size:12px;margin-right:10px"
                      @click="handlePreview(scope.row)">预览</span>
                      <span style="cursor: pointer; color:blue;font-size:12px" v-if="!formDisabled" @click="uploadRemove(scope.row)" >删除</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="备注" width="200">
                  <template slot-scope="scope">
                    <el-input maxlength="200" v-model="scope.row.remark" placeholder="请输入" :disabled="!scope.row.typeId">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80px" fixed="right">
                  <template slot-scope="scope">
                    <el-button circle @click="add('stayList', scope.$index, applyIndex)" class="noHover"
                      icon="el-icon-plus">
                    </el-button>
                    <el-button circle style="" @click="pop('stayList', scope.$index, applyIndex)" class="noHover"
                      icon="el-icon-minus">
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="_table" v-if="applyItem.subsidyList.length">
              <div class="padding20">报销科目：差旅-差旅补助<span style="margin-left: 100px;">报销金额小计：{{
                (price[applyIndex].subsidyList.fee).toFixed(2) }}元，票据张数小计：{{ bill[applyIndex].subsidyList.bill
  }}张</span></div>
              <el-table :data="applyItem.subsidyList" border style="width: 100%">
                <el-table-column label="报销科目" width="166">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.typeId" placeholder="请选择">
                      <template v-for="item in optionTypeList">
                        <el-option v-if="item.type == 1001 && item.sign == 3" :key="item.id" :label="item.name"
                          :value="item.id" @click.native="
                            chooseType(
                              item.type,
                              item.sign,
                              scope,
                              'subsidyList',
                              item.must,
                              item.state,
                              applyIndex
                            )
                            ">
                        </el-option>
                      </template>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="开始日期" width="200">
                  <template slot-scope="scope">
                    <el-date-picker style="width: 90%" v-model="scope.row.startDate" type="date"
                      :disabled="!scope.row.typeId" :picker-options="pickerOptions(
                        scope.row,
                        'subsidyList',
                        scope.row.startDate,
                        scope.$index,
                        'startDate',
                        applyIndex
                      )
                        " value-format="yyyy-MM-dd" placeholder="请选择" @change="
    checkProjectReimbursement($event, scope, 'subsidyList', applyIndex)
    ">
                    </el-date-picker>
                  </template>
                </el-table-column>
                <el-table-column label="上午/下午" width="120">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.subsidyList.' + scope.$index + '.startSign'" :rules="{
    required: scope.row.startDate ? true : false,
    message: '请输入',
    trigger: 'blur',
  }">
                      <el-select v-model="scope.row.startSign" placeholder="请选择"
                        :disabled="scope.row.startDate ? false : true">
                        <el-option v-for="item in dateList" :key="item.value" :label="item.label" :value="item.value"
                          @click.native="
                            checkProjectReimbursement(
                              $event,
                              scope,
                              'subsidyList', applyIndex
                            )
                            ">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="结束日期" width="200">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.subsidyList.' + scope.$index + '.endDate'" :rules="{
    required: scope.row.startDate ? true : false,
    message: '请输入',
    trigger: 'blur',
  }">
                      <el-date-picker :disabled="scope.row.startSign || scope.row.startSign == 0
                        ? false
                        : true
                        " style="width: 90%" v-model="scope.row.endDate" type="date" value-format="yyyy-MM-dd"
                        :picker-options="pickerOptions(
                          scope.row,
                          'subsidyList',
                          scope.row.endDate,
                          scope.$index,
                          'endDate',
                          applyIndex
                        )
                          " placeholder="请选择" @change="
    checkProjectReimbursement(
      $event,
      scope,
      'subsidyList', applyIndex
    )
    ">
                      </el-date-picker>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="上午/下午" width="120">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.subsidyList.' + scope.$index + '.endSign'" :rules="{
    required: scope.row.startDate ? true : false,
    message: '请输入',
    trigger: 'blur',
  }">
                      <el-select v-model="scope.row.endSign" placeholder="请选择">
                        <el-option v-for="item in dateList" :key="item.value" :label="item.label" :value="item.value"
                          @click.native="
                            checkProjectReimbursement(
                              $event,
                              scope,
                              'subsidyList', applyIndex
                            )
                            ">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="工作日天数" width="166">
                  <template slot-scope="scope">
                    <span>{{ scope.row.workDays }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="加班天数" width="166">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.workOvertimes" maxlength="3" placeholder="请输入" :disabled="!scope.row.typeId"
                      @input="changeInput($event, scope, 'subsidyList', applyIndex)"
                      @blur="setWorkOverTimes(scope.row.workOvertimes, scope.row)"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="出差地" width="166">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.subsidyList.' + scope.$index + '.endAddr'" :rules="{
    required: scope.row.startDate ? true : false,
    message: '请输入',
    trigger: 'blur',
  }">
                      <el-input v-model="scope.row.endAddr" placeholder="请输入" maxlength="30" show-word-limit>
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="补贴标准(元/天)" width="166">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.subsidyList.' + scope.$index + '.standard'" :rules="{
    required: scope.row.startDate ? true : false,
    message: '请输入',
    trigger: 'blur',
  }">
                      <el-input v-model="scope.row.standard" placeholder="请输入" maxlength="10"
                        @input="commonPrice($event, scope, 'subsidyList', applyIndex)"
                        @change="sumFee('subsidyList', applyIndex)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="报销金额(元)" width="166">
                  <template slot-scope="scope">
                    <!-- <el-input
                  v-model="scope.row.fee"
                  placeholder="请输入内容"
                ></el-input> -->
                    <span>{{ scope.row.fee }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="附件" width="120">
                  <template slot-scope="scope">
                    <el-upload class="upload" :data="{ type: 33 }" :action="uploadFileUrl" :headers="headers"
                      :file-list="scope.row.fileList"
                      :on-success="(e, file, fileList) => {
                        return uploadSuccess(e, file, fileList, scope, 1)}"
                      :class="{ disUoloadSty: scope.row.noneBtn }"
                      v-if="scope.row.fileList.length == 0"
                      :limit="1"
                      :before-upload="beforeUpload">
                      <el-button type="button" class="ant-btn">
                        <span>文件上传</span>
                      </el-button>
                    </el-upload>
                    <div v-else>
                      <span style="cursor: pointer; color:blue;font-size:12px;margin-right:10px"
                      @click="showUserDocumentFile(scope.row)">下载</span>
                      <span style="cursor: pointer; color:blue;font-size:12px;margin-right:10px"
                      @click="handlePreview(scope.row)">预览</span>
                      <span style="cursor: pointer; color:blue;font-size:12px" v-if="!formDisabled" @click="uploadRemove(scope.row)" >删除</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="备注" width="200">
                  <template slot-scope="scope">
                    <el-input maxlength="200" :disabled="!scope.row.typeId" v-model="scope.row.remark" placeholder="请输入">
                    </el-input>
                  </template>
                  差旅报销申请
                </el-table-column>
                <el-table-column label="操作" width="80px" fixed="right">
                  <template slot-scope="scope">
                    <el-button circle @click="add('subsidyList', scope.$index, applyIndex)" class="noHover"
                      icon="el-icon-plus">
                    </el-button>
                    <el-button circle @click="pop('subsidyList', scope.$index, applyIndex)" class="noHover"
                      icon="el-icon-minus">
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="_table" v-if="applyItem.otherList.length">
              <div class="padding20">报销科目：其他<span style="margin-left: 100px;">报销金额小计：{{
                (price[applyIndex].otherList.fee).toFixed(2) }}元，票据张数小计：{{ bill[applyIndex].otherList.bill }}张</span>
              </div>
              <el-table :data="applyItem.otherList" border style="width: 100%">
                <el-table-column label="报销科目" width="166">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.typeId" placeholder="请选择">
                      <template v-for="item in optionTypeList">
                        <el-option v-if="item.type != 1001 && item.travel == 1" :key="item.id" :label="item.name"
                          :value="item.id" @click.native="
                            chooseType(
                              item.type,
                              item.sign,
                              scope,
                              'otherList',
                              item.must,
                              item.state,
                              applyIndex
                            )
                            ">
                        </el-option>
                      </template>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="出差地">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.otherList.' + scope.$index + '.endAddr'" :rules="{
    required: scope.row.typeId ? true : false,
    message: '请选择',
    trigger: 'change',
  }">
                      <el-input maxlength="30" show-word-limit v-model="scope.row.endAddr" placeholder="请输入">
                      </el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="报销金额(元)">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' +
                      applyIndex +
                      '.otherList.' + scope.$index + '.fee'" :rules="{
    required: scope.row.typeId ? true : false,
    message: '',
    trigger: 'change',
  }">
                      <el-input v-model="scope.row.fee" maxlength="10" :disabled="!scope.row.typeId" placeholder="请输入"
                        @input="commonPrice($event, scope, 'otherList', applyIndex)"
                        @change="sumFee('otherList', applyIndex)"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="发票张数">
                  <template slot-scope="scope">
                    <el-form-item label=" " :prop="'applyList.' + applyIndex + '.otherList.' + scope.$index + '.invoice'"
                      :rules="{
                        required: !!scope.row.fee,
                        message: '请输入',
                        trigger: 'blur',
                      }">
                      <el-input @input="commonInvoice($event, scope.row)" v-model="scope.row.invoice"
                        :disabled="!scope.row.typeId" placeholder="请输入" @change="sumBill('otherList', applyIndex)"
                        maxlength="3"></el-input>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="附件" width="120">
                  <template slot-scope="scope">
                    <el-upload class="upload" :data="{ type: 33 }" :action="uploadFileUrl" :headers="headers"
                      :file-list="scope.row.fileList"
                      :on-success="(e, file, fileList) => {
                      return uploadSuccess(e, file, fileList, scope, 1)}"
                      :class="{ disUoloadSty: scope.row.noneBtn }"
                      v-if="scope.row.fileList.length == 0"
                      :limit="1"
                      :before-upload="beforeUpload">
                      <el-button type="button" class="ant-btn">
                        <span>文件上传</span>
                      </el-button>
                    </el-upload>
                    <div v-else>
                      <span style="cursor: pointer; color:blue;font-size:12px;margin-right:10px"
                      @click="showUserDocumentFile(scope.row)">下载</span>
                      <span style="cursor: pointer; color:blue;font-size:12px;margin-right:10px"
                      @click="handlePreview(scope.row)">预览</span>
                      <span style="cursor: pointer; color:blue;font-size:12px" v-if="!formDisabled" @click="uploadRemove(scope.row)" >删除</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="备注" width="200">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.remark" :disabled="!scope.row.typeId" maxlength="200" placeholder="请输入">
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80px" fixed="right">
                  <template slot-scope="scope">
                    <el-button circle @click="add('otherList', scope.$index, applyIndex)" class="noHover"
                      icon="el-icon-plus">
                    </el-button>
                    <el-button circle @click="pop('otherList', scope.$index, applyIndex)" class="noHover"
                      icon="el-icon-minus">
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="addApply" v-if="queryParams.applyList.length - 1 == applyIndex && !formDisabled"
            @click="addApplyClick()">
            <img src="@/assets/icons/addApply.png" alt="" style="height: 18px; width: 18px; cursor: pointer">
            <span>差旅交通段</span>
          </div>
        </div>
        <!-- 附件组件 -->
        <!-- <Appendix :formDisabled="formDisabled" :fileList="fileList" :attachmentIds="this.queryParams.attachmentIds"
          :appendixType="33" :choose="true" @upLoad="upLoad" @deleteFile="deleteFile" /> -->
      </el-form>
      <!-- 按钮组件 -->
      <operation-button ref="operationButton" :approveStatus="transmitParams.status" :id="transmitParams.id"
        :showBack="formDisabled" :type="transmitParams.type" :whetherNextApprove="transmitParams.whetherNextApprove"
        @saveSubmit="saveSubmit" :whetherEdit="'false'"/>
    </div>

    <!-- 右侧弹窗 -->
    <Drawer :drawerForm="drawerForm" :buttonType="buttonType" :formTitle="title" @changeButtonType="changeButtonType" />
		<el-dialog title="预览" :visible.sync="dialogPublish" width="1200px">
			<div class="publishValue" v-if="dialogPublish">
				<iframe id="frame" :src="previewUrl" style="width: 100%;height:500px;border: 0px;margin:0;"
					></iframe>
			</div>
		</el-dialog>
  </div>
</template>
<script>
import UserByDeptSelect from "@/components/UserByDeptSelect/indexRecord"
import {
  getLockerAmount,
  getBankInfo,
  // 1.4
  checkProjectReimbursement,
  //1.7
  reimbursementType,
  postSaveOrUpdateData,
  getTraffic,
  getTrafficCity,
  getDetailTravel,
  viewTravel
} from '@/api/ec/projectReimbursement/index'

const Base64 = require('js-base64').Base64
import operationButton from '@/components/OperationButton/index'
import Question from '@/components/Question/index'
import projectSelect from '@/components/ProjectSelect/index'
import peopleSelect from '@/components/PeopleSelect/index'
// import SelectDate from "@/components/SelectDate/index";
import Drawer from '@/components/Drawer/index'
import Appendix from '@/components/Appendix/index'
import { mapGetters } from 'vuex'
import Subtitle from '@/components/Subtitle/index' //引入小标题
import TopTitle from '@/components/TopTitle/index' //引入头部标题
import { areaInfo } from '@/api/system/region'
import { getFlyback } from '@/api/oa/flybackPerson'
import { deepClone } from '@/utils'
import Company from '@/components/Company/index'
import { getInfo } from '@/api/login'
import { getToken } from "@/utils/auth";
import { getOrganizational } from '@/api/oa/employeeCare/index'
let nowDate = new Date()
let day = nowDate.getDate()
let month = nowDate.getMonth() + 1
let year = nowDate.getFullYear()
let char = '-'
//补全0，并拼接
let nowTime = year + char + completeDate(month) + char + completeDate(day)

//补全0
function completeDate(value) {
  return value < 10 ? '0' + value : value
}
import dayjs from 'dayjs'
import { removeFile } from "@/common/common"

export default {
  name: 'ProjectReimbursement',
  //注入reload方法
  inject: ['reload'],
  components: {
    UserByDeptSelect,
    operationButton,
    Company,
    Drawer,
    projectSelect,
    peopleSelect,
    Appendix,
    Subtitle,
    TopTitle,
    Question
  },
  data() {
    let validateName = (rule, value, callback) => {
      const reg = /^[^#%&，。！……@￥!*\/|:<>?\"\d]*$/;
      if (reg.test(value)) {
        callback();
      } else {
        callback(new Error('请输入正确的领款人'));
      }
    };
    return {
      plate:'1',
      previewUrl:'',
      dialogPublish:false,
      //上传
      uploadFileUrl: window.CONFIG.VUE_APP_BASE_API + "/file/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      bill: [
        {
          sum: 0,
          trafficList: { bill: 0 },
          subsidyList: { bill: 0 },
          stayList: { bill: 0 },
          otherList: { bill: 0 },
        }
      ],//票据
      price: [
        {
          sum: 0,
          trafficList: { fee: 0 },
          subsidyList: { fee: 0 },
          stayList: { fee: 0 },
          otherList: { fee: 0 },
        }
      ],//价格
      buttons: [
        { type: 1, name: '差旅交通费' },
        { type: 2, name: '差旅住宿费' },
        { type: 3, name: '差旅补助' },
        { type: 4, name: '其他费用' },
      ],
      orgList: [],
      title: '差旅报销申请',
      createTime: '',
      createTime1: '',
      nowTime: nowTime,
      preview: false,
      isFlyPower: true,
      must: null,
      index1: [],
      cityIsVisible: false,
      index2: [],
      queryParams: {
        createByName: '',
        writeOffFee: '0.00',
        invoice: 0,
        parentCompanyName: '',
        totalFee: 0,
        fly: 0,
        type: 1001,
        needPayFee: 0,
        attachmentIds: [],
        payeeAccount: '',
        remark: null,
        applyList: [
          {
            trafficList: [], //项目报销明细（差旅--交通费）
            subsidyList: [], //项目报销明细（差旅--差旅补贴）
            stayList: [], //项目报销明细（差旅--住宿费）
            purchaseList: [], //项目报销明细（采购/事项申请报销）
            otherList: [] //项目报销明细(差旅-其他)
          }
        ]
      },
      applyItemObj: {
        trafficList: [], //项目报销明细（差旅--交通费）
        subsidyList: [], //项目报销明细（差旅--差旅补贴）
        stayList: [], //项目报销明细（差旅--住宿费）
        purchaseList: [], //项目报销明细（采购/事项申请报销）
        otherList: [] //项目报销明细(差旅-其他)
      },
      // 报销科目
      optionTypeList: [],
      // 交通工具
      trafficList: [],
      // 市内交通工具
      trafficCityList: [],
      // 传递参数
      transmitParams: {},
      // 按钮类型
      buttonType: null,
      // 文件列表
      fileList: [],
      // 回显文件列表
      echoFileList: [],
      // 右侧弹窗form
      drawerForm: {
        id: null,
        reason: null
      },
      dateList: [
        { label: '全天', value: 0 },
        { label: '上午', value: 1 },
        { label: '下午', value: 2 }
      ],

      // 表单是否不可输入
      formDisabled: false,
      rules: {
        projectName: [
          { required: true, message: '请选择项目', trigger: 'change' }
        ],
        companyId: [
          { required: false, message: '请选择付款主体公司', trigger: 'change' }
        ],
        userName: [
          { required: true, message: '请选择报销人员', trigger: 'change' }
        ],
        collection: [
          { required: true, message: '请输入领款人', trigger: 'blur' },
          { pattern: /^[\u4e00-\u9fa5]+$/, message: "请输入中文", trigger: 'blur' }
        ],
        payeeAccount: [
          { required: false, message: '请输入收款账户', trigger: 'blur' }
        ],
        openBank: [
          { required: false, message: '请输入账户开户行', trigger: 'blur' }
        ],
        happenedDate: [
          { required: true, message: '请输入费用发生时间', trigger: 'blur' }
        ],
        // flyBackId:[{ required: true, message: '请选择Flyback申请', trigger: 'blur' }
        // ],
        parentCompanyName: [{ required: true, message: '请选择付款主体公司', trigger: 'blur' }]
      },
      ifGetLeftFee: false,
      cityOptions: [],
      flybackOptions: [],
    }
  },
  computed: {
    ...mapGetters(['dictList']),
    totalFee() {
      var total = 0
      const arr = ['trafficList', 'subsidyList', 'stayList', 'otherList']
      arr.forEach((key) => {
          this.queryParams.applyList.forEach((child) => {
            child[key].forEach((item) => {
              if (item.fee) {
                total = this.Add(total, Number(item.fee), 2)
              }
            })
          })
      })
      return total.toFixed(2)
    },
    invoiceNum() {
      var num = 0
      const arr = ['trafficList', 'stayList', 'otherList']
      arr.forEach((key) => {
        this.queryParams.applyList.forEach((child) => {
          child[key].forEach((item) => {
            if (item.invoice) {
              num = this.Add(num, Number(item.invoice), 2)
            }
          })
        })
      })
      return num
    },
    pickerOptions() {
      return function (row, listKey, date, rowIndex, timeKey, listIndex) {
        const that = this
        return {
          disabledDate(time) {
            let disabled = false
            if (time.getTime() > Date.now() - 8.64e6) disabled = true
            that.queryParams.applyList.forEach((child, childIndex) => {
              child[listKey].forEach((item, index) => {
                //项目报销明细（差旅--交通费）trafficList
                //项目报销明细（差旅--差旅补贴） subsidyList
                //项目报销明细（差旅--住宿费）stayList
                if (listKey == 'trafficList') {
                  item.startDate = item.trafficDate
                  if ((index != rowIndex || childIndex != listIndex) && row.typeId == item.typeId) {
                    // if (
                    //   time.getTime() >= that.fomrmatDate(item.startDate) &&
                    //   time.getTime() <= that.fomrmatDate(item.endDate)
                    // )
                    //   disabled = true;
                    if (row.typeId == '19') {
                      if (
                        timeKey == 'startDate' &&
                        time.getTime() >= that.fomrmatDate(item.startDate) &&
                        time.getTime() < that.fomrmatDate(item.endDate)
                      ) {
                        disabled = true
                      }
                      if (
                        timeKey == 'startDate' &&
                        row.endDate &&
                        item.endDate &&
                        that.fomrmatDate(row.endDate) >=
                        that.fomrmatDate(item.endDate)
                      ) {
                        if (time.getTime() <= that.fomrmatDate(item.endDate)) {
                          disabled = true
                        }
                      }
                      if (
                        timeKey == 'endDate' &&
                        row.startDate &&
                        item.startDate &&
                        that.fomrmatDate(row.startDate) <
                        that.fomrmatDate(item.startDate)
                      ) {
                        if (
                          time.getTime() >=
                          that.fomrmatDate(item.startDate) + 24 * 60 * 60 * 1000
                        ) {
                          disabled = true
                        }
                      }
                    } else {
                      if (
                        timeKey == 'startDate' &&
                        row.endDate &&
                        item.endDate &&
                        that.fomrmatDate(row.endDate) >
                        that.fomrmatDate(item.endDate)
                      ) {
                        if (time.getTime() < that.fomrmatDate(item.endDate)) {
                          disabled = true
                        }
                      }
                      if (
                        timeKey == 'endDate' &&
                        row.startDate &&
                        item.startDate &&
                        that.fomrmatDate(row.startDate) <
                        that.fomrmatDate(item.startDate)
                      ) {
                        if (time.getTime() > that.fomrmatDate(item.startDate)) {
                          disabled = true
                        }
                      }
                    }

                  } else if (index == rowIndex && childIndex == listIndex) {
                    if (
                      timeKey == 'startDate' &&
                      item.endDate &&
                      time.getTime() > that.fomrmatDate(item.endDate)
                    ) {
                      disabled = true
                    }
                    if (
                      timeKey == 'endDate' &&
                      item.startDate &&
                      time.getTime() < that.fomrmatDate(item.startDate)
                    ) {
                      disabled = true
                    }
                  }
                } else if (listKey == 'stayList') {
                  if ((index != rowIndex || childIndex != listIndex) && row.typeId == item.typeId) {
                    if (
                      timeKey == 'startDate' &&
                      time.getTime() >= that.fomrmatDate(item.startDate) &&
                      time.getTime() < that.fomrmatDate(item.endDate)
                    ) {
                      disabled = true
                    }
                    if (
                      timeKey == 'startDate' &&
                      row.endDate &&
                      item.endDate &&
                      that.fomrmatDate(row.endDate) >=
                      that.fomrmatDate(item.endDate)
                    ) {
                      if (time.getTime() <= that.fomrmatDate(item.endDate)) {
                        disabled = true
                      }
                    }
                    if (
                      timeKey == 'endDate' &&
                      row.startDate &&
                      item.startDate &&
                      that.fomrmatDate(row.startDate) <
                      that.fomrmatDate(item.startDate)
                    ) {
                      if (
                        time.getTime() >=
                        that.fomrmatDate(item.startDate) + 24 * 60 * 60 * 1000
                      ) {
                        disabled = true
                      }
                    }
                  } else if (childIndex == listIndex && index == rowIndex) {
                    if (
                      timeKey == 'startDate' &&
                      item.endDate &&
                      time.getTime() >= that.fomrmatDate(item.endDate)
                    ) {
                      disabled = true
                    }
                    if (
                      timeKey == 'endDate' &&
                      item.startDate &&
                      time.getTime() <= that.fomrmatDate(item.startDate)
                    ) {
                      disabled = true
                    }
                  }
                } else {
                  if ((index != rowIndex || childIndex != listIndex) && row.typeId == item.typeId) {
                    if (
                      time.getTime() >= that.fomrmatDate(item.startDate) &&
                      time.getTime() <= that.fomrmatDate(item.endDate)
                    ) {
                      disabled = true
                    }
                    if (
                      timeKey == 'startDate' &&
                      row.endDate &&
                      item.endDate &&
                      that.fomrmatDate(row.endDate) >=
                      that.fomrmatDate(item.endDate)
                    ) {
                      if (time.getTime() <= that.fomrmatDate(item.endDate)) {
                        disabled = true
                      }
                    }
                    if (
                      timeKey == 'endDate' &&
                      row.startDate &&
                      item.startDate &&
                      that.fomrmatDate(row.startDate) <=
                      that.fomrmatDate(item.startDate)
                    ) {
                      if (time.getTime() >= that.fomrmatDate(item.startDate)) {
                        disabled = true
                      }
                    }
                  } else if (childIndex == listIndex && index == rowIndex) {
                    if (
                      timeKey == 'startDate' &&
                      item.endDate &&
                      time.getTime() > that.fomrmatDate(item.endDate)
                    ) {
                      disabled = true
                    }
                    if (
                      timeKey == 'endDate' &&
                      item.startDate &&
                      time.getTime() < that.fomrmatDate(item.startDate)
                    ) {
                      disabled = true
                    }
                  }
                }
              })
            })

            return disabled
          }
        }
      }
    },
  },
  watch: {
    totalFee(newVal) {
      this.$set(this.queryParams, 'totalFee', newVal)
      if (newVal >= this.queryParams.writeOffFee) {
        if (
          !this.queryParams.id ||
          (this.queryParams.id && this.queryParams.pass == 1)
        ) {
          this.$set(
            this.queryParams,
            'needPayFee',
            this.Sub(newVal, this.queryParams.writeOffFee, 2)
          )
        }
      } else {
        this.$message.warning('冲销备用金金额不能大于合计报销金额！')
      }
    },
    invoiceNum(newVal) {
      this.$set(this.queryParams, 'invoice', newVal)
    },
    'queryParams.writeOffFee'(newVal) {
      //leftFee备用金剩余金额 writeOffFee冲销备用金金额 totalFee报销金额合计 needPayFee财务支付金额
      if (Number(this.queryParams.leftFee) < Number(newVal)) {
        if (
          !this.queryParams.id ||
          (this.queryParams.id &&
            this.queryParams.pass == 1 &&
            this.ifGetLeftFee)
        ) {
          this.$message.warning('冲销备用金金额不能大于备用金剩余金额！')
          this.$set(this.queryParams, 'writeOffFee', this.queryParams.leftFee)
        }
      } else if (Number(this.queryParams.totalFee) >= Number(newVal)) {
        if (
          !this.queryParams.id ||
          (this.queryParams.id && this.queryParams.pass == 1)
        ) {
          this.$set(
            this.queryParams,
            'needPayFee',
            this.Sub(this.queryParams.totalFee, newVal, 2)
          )
        }
      } else {
        this.$message.warning('冲销备用金金额不能大于合计报销金额！')
        this.$set(this.queryParams, 'writeOffFee', this.queryParams.totalFee)
      }
    }
  },
  async created() {
    await this.getTraffic()
    this.init()
    this.reimbursementType()
    this.getBankInformation()
    this.getOrgListInfo()
  },
  methods: {
    selectCopyDelivery(userIds, userNames, multiSelect, index) {
      this.$set(this.queryParams, "collection", userNames)
      this.queryParams.collection = userNames
      this.$refs.queryParams.clearValidate('collection')
    },
    setWorkOverTimes(val, row) {
      if (val) {
        row.workOvertimes = parseFloat(val)
      }
    },
		//下载附件
    showUserDocumentFile(row) {
      let url = row.fileList[0].previewUrl ? row.fileList[0].previewUrl : row.fileList[0].url
      saveAs(url, row.fileList[0].name);
    },
    //查看附件
    handlePreview(file) {
      let url = file.fileList[0].previewUrl ? file.fileList[0].previewUrl : file.fileList[0].url
      this.previewUrl = this.viewFileUrl + encodeURIComponent(Base64.encode(url))
      this.dialogPublish = true
      // if (!file.fileList[0].name.includes('.zip') && !file.fileList[0].name.includes('.rar')) {
      //   // window.open(this.viewFileUrl + encodeURIComponent(Base64.encode(url)))
      //   this.previewUrl = this.viewFileUrl + encodeURIComponent(Base64.encode(url))
      //   this.dialogPublish = true
      // }else{
      //   this.$message.error('暂不支持在线预览该格式文件')
      // }
    },
    //上传文件
    beforeUpload(file) {
      const testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const arrAy =  ["jpg", "jpeg", "png","doc", "docx", "xls", "xlsx","pdf", "ppt" ,"pptx"]
      const extension = testmsg
      if (arrAy.indexOf(testmsg)===-1) {
        this.$message({
          message: '当前支持上传文件格式包括"jpg", "jpeg", "png","doc", "docx", "xls", "xlsx","pdf", "ppt" ,"pptx"',
          type: 'warning'
        })
        return false;
      }
      else {
        extension === true
      }
      const isLt2M = file.size / 1024 / 1024 < 60    //这里做文件大小限制
      if (!isLt2M) {
        this.$message({
          message: '上传文件大小不能超过 60MB!',
          type: 'warning'
        })
      }
      return extension && isLt2M
    },
    uploadSuccess(e, file, fileList, scope, type) {
      if (e.code !== 200) {
        this.$message.error(e.msg);
          scope.row.noneBtn = false
      } else {
        if (type == 1) {
          scope.row.fileList.push({
            name: file.name,
            id: file.response.data.id,
            url: e.data.previewUrl,
            previewUrl: e.data.previewUrl
          })
          scope.row.attachmentIds.push(
            file.response.data.id
          )
          scope.row.noneBtn = true
        } else {
          scope.row.attachmentIds.push({
            name: file.name,
            id: file.response.data.id
          })
          scope.row.annexNoneBtn = true
        }
      }
    },
    uploadRemove(row) {
      this.id = row.fileList ? row.fileList[0].id:''
      removeFile(this.id).then((res) => {
        if (res.code === 200) {
						row.fileList = []
						row.noneBtn = false
        }
      })
    },
    sumBill(key, applyIndex) {
      if (key == 'trafficList') {
        let invoice = 0
        this.queryParams.applyList[applyIndex].trafficList.forEach(item => {
          invoice += Number(item.invoice ? item.invoice : 0)
        })
        console.log(invoice, this.queryParams.applyList[applyIndex].trafficList);
        this.bill[applyIndex].trafficList.bill = invoice
      }
      if (key == 'stayList') {
        let invoice = 0
        this.queryParams.applyList[applyIndex].stayList.forEach(item => {
          invoice += Number(item.invoice ? item.invoice : 0)
        })
        this.bill[applyIndex].stayList.bill = invoice
      }
      if (key == 'subsidyList') {
        let invoice = 0
        this.queryParams.applyList[applyIndex].subsidyList.forEach(item => {
          invoice += Number(item.invoice ? item.invoice : 0)
        })
        this.bill[applyIndex].subsidyList.bill = invoice
      }
      if (key == 'otherList') {
        let invoice = 0
        this.queryParams.applyList[applyIndex].otherList.forEach(item => {
          invoice += Number(item.invoice ? item.invoice : 0)
        })
        this.bill[applyIndex].otherList.bill = invoice
      }
      var totle = 0
      const arr = ['trafficList', 'subsidyList', 'stayList', 'otherList']
      arr.forEach(key => {
        this.queryParams.applyList[applyIndex][key].forEach(item => {
          if (item.invoice) {
            totle += Number(item.invoice ? item.invoice : 0)
          }
        })
      })
      this.bill[applyIndex].sum = totle
    },
    sumFee(key, applyIndex) {
      console.log(key, applyIndex);
      if (key == 'trafficList') {
        let fee = 0
        this.queryParams.applyList[applyIndex].trafficList.forEach(item => {
          fee += Number(item.fee ? item.fee : 0)
        })
        this.price[applyIndex].trafficList.fee = fee
      }
      if (key == 'stayList') {
        let fee = 0
        this.queryParams.applyList[applyIndex].stayList.forEach(item => {
          fee += Number(item.fee ? item.fee : 0)
        })
        this.price[applyIndex].stayList.fee = fee
      }
      if (key == 'subsidyList') {
        let fee = 0
        this.queryParams.applyList[applyIndex].subsidyList.forEach(item => {
          fee += Number(item.fee ? item.fee : 0)
        })
        this.price[applyIndex].subsidyList.fee = fee
      }
      if (key == 'otherList') {
        let fee = 0
        this.queryParams.applyList[applyIndex].otherList.forEach(item => {
          fee += Number(item.fee ? item.fee : 0)
        })
        this.price[applyIndex].otherList.fee = fee
      }
      var sum = 0
      const arr = ['trafficList', 'subsidyList', 'stayList', 'otherList']
      arr.forEach(key => {
        this.queryParams.applyList[applyIndex][key].forEach(item => {
          if (item.fee) {
            sum += Number(item.fee ? item.fee : 0)
          }
        })
      })
      this.price[applyIndex].sum = sum
      console.log(this.price);
    },
    // 计算
    isShow(type, index) {
      switch (type) {
        case 1:
          if (this.queryParams.applyList[index].trafficList.length > 0) {
            this.queryParams.applyList[index].trafficList = []
          } else {
            console.log(Number(this.queryParams.applyList[index].startAddrId));
            this.queryParams.applyList[index].trafficList = [
              {
                trafficPhase: index,
                typeId: '',
                userId: this.$store.getters.userId,
                type: 1001,
                sign: 2,
                state: 0,
                fileList: [],
                fileName: '',
                attachmentIds: [],
                startId: this.queryParams.applyList[index].startAddrId ? this.queryParams.applyList[index].startAddrId : 0,
                endId: this.queryParams.applyList[index].endAddrId ? this.queryParams.applyList[index].endAddrId : 0,
                startAddr: this.queryParams.applyList[index].startAddrId ? this.recursion(this.cityOptions, Number(this.queryParams.applyList[index].startAddrId)).areaName : '',
                endAddr: this.queryParams.applyList[index].endAddrId ? this.recursion(this.cityOptions, Number(this.queryParams.applyList[index].endAddrId)).areaName : ''
              }
            ]
            // this.$set(this.queryParams.applyList[index],'startAddr',this.recursion(this.cityOptions,this.queryParams.applyList.startAddrId))
          }
          break;
        case 2:
          if (this.queryParams.applyList[index].stayList.length > 0) {
            this.queryParams.applyList[index].stayList = []
          } else {
            this.queryParams.applyList[index].stayList = [
              {
                trafficPhase: index,
                typeId: '',
                type: 1001,
                sign: 1,
                userId: this.$store.getters.userId,
                fileList: [],
                fileName: '',
                attachmentIds: [],
                startId: this.queryParams.applyList[index].startAddrId ? this.queryParams.applyList[index].startAddrId : 0,
                endId: this.queryParams.applyList[index].endAddrId ? this.queryParams.applyList[index].endAddrId : 0,
                startAddr: this.queryParams.applyList[index].startAddrId ? this.recursion(this.cityOptions, Number(this.queryParams.applyList[index].startAddrId)).areaName : '',
                endAddr: this.queryParams.applyList[index].endAddrId ? this.recursion(this.cityOptions, Number(this.queryParams.applyList[index].endAddrId)).areaName : ''
              }
            ]
          }
          break;
        case 3:
          if (this.queryParams.applyList[index].subsidyList.length > 0) {
            this.queryParams.applyList[index].subsidyList = []
          } else {
            this.queryParams.applyList[index].subsidyList = [
              {
                trafficPhase: index,
                typeId: '',
                type: 1001,
                sign: 3,
                userId: this.$store.getters.userId,
                fileList: [],
                fileName: '',
                attachmentIds: [],
                startId: this.queryParams.applyList[index].startAddrId ? this.queryParams.applyList[index].startAddrId : 0,
                endId: this.queryParams.applyList[index].endAddrId ? this.queryParams.applyList[index].endAddrId : 0,
                startAddr: this.queryParams.applyList[index].startAddrId ? this.recursion(this.cityOptions, Number(this.queryParams.applyList[index].startAddrId)).areaName : '',
                endAddr: this.queryParams.applyList[index].endAddrId ? this.recursion(this.cityOptions, Number(this.queryParams.applyList[index].endAddrId)).areaName : ''
              }
            ]
          }
          break;
        case 4:
          if (this.queryParams.applyList[index].otherList.length > 0) {
            this.queryParams.applyList[index].otherList = []
          } else {
            this.queryParams.applyList[index].otherList = [
              {
                trafficPhase: index,
                userId: this.$store.getters.userId,
                type: 1001,
                sign: -1,
                fileList: [],
                fileName: '',
                attachmentIds: [],
                startId: this.queryParams.applyList[index].startAddrId ? this.queryParams.applyList[index].startAddrId : 0,
                endId: this.queryParams.applyList[index].endAddrId ? this.queryParams.applyList[index].endAddrId : 0,
                startAddr: this.queryParams.applyList[index].startAddrId ? this.recursion(this.cityOptions, Number(this.queryParams.applyList[index].startAddrId)).areaName : '',
                endAddr: this.queryParams.applyList[index].endAddrId ? this.recursion(this.cityOptions, Number(this.queryParams.applyList[index].endAddrId)).areaName : ''
              }
            ]
          }
          break;
      }
    },
    // 获取组织列表
    getOrgListInfo() {
      getOrganizational(JSON.stringify({ parentId: "0" })).then((res) => {
        this.orgList = res.data.rows;
      });
    },

    showProject() {
      console.log(this.queryParams)
      if (this.queryParams.projectId) {
        this.$router.push({
          path: "/projectManage/ledgerInfo/info/" + this.queryParams.projectId,
          query: { type: 2 },
        });
      }
    },
    cascaderClick(data, val) {
      console.log(data)
      // this.$nextTick(() => {
      //   this.$refs[val][0].checkedValue = data.id;
      // })
    },
    changeFly(e) {
      this.queryParams.fly = e
    },
    //删除为空的children，否则影响展示效果
    removeCityEmptyChild(arr) {
      arr.forEach(item => {
        if (item.children.length === 0) {
          delete item.children
        } else {
          return this.removeCityEmptyChild(item.children)
        }
      })
      return arr
    },
    selectCompany(id, name) {
      this.$delete(this.queryParams, 'companyId')
      this.$delete(this.queryParams, 'parentCompanyName')
      this.$set(this.queryParams, 'companyId', id)
      this.$set(this.queryParams, 'parentCompanyName', name)
      this.queryParams.companyId = id
      this.queryParams.parentCompanyName = name
    },
    // 1.7  start
    reimbursementType() {
      reimbursementType({}).then((res) => {
        this.optionTypeList = res.data
      })
    },
    async getTraffic() {
      const trafficList = await getTraffic()
      this.trafficList = trafficList.data
      const trafficCityList = await getTrafficCity()
      this.trafficCityList = trafficCityList.data
      const city = await areaInfo()
      this.cityOptions = this.removeCityEmptyChild(city.data)
      getFlyback(this.$store.getters.userId).then(res => {
        this.flybackOptions = res.data
      })
    },
    flybackSelect(val) {
      let result = this.flybackOptions.filter(item => item.id === val)[0]
      this.$set(this.queryParams, 'projectName', result.projectName)
      this.$set(this.queryParams, 'projectId', result.projectId)
      this.$set(this.queryParams, 'code', result.projectCode)
      this.queryParams.applyList = []
      result.recordList.forEach((item, index) => {
        let obj = deepClone(this.applyItemObj)
        obj.startAddrId = item.startAdr
        obj.endAddrId = item.endAdr
        obj.trafficList[0].traffic = item.vehicle
        obj.trafficList[0].trafficDate = item.startDate
        obj.trafficList[0].endDate = item.endDate
        obj.trafficList[0].fee = item.money
        obj.trafficList[0].remark = item.remark
        obj.trafficList[0].invoice = 1
        obj.trafficList[0].trafficPhase = 1
        this.queryParams.applyList.push(obj)
        this.cityChange(item.startAdr, index, 'start')
        this.cityChange(item.endAdr, index, 'end')
      })
    },
    hideShowClick(index) {
      this.$set(this.queryParams.applyList[index], 'hide', !this.queryParams.applyList[index].hide)
    },
    recursion(arr, id) {
      let result = null
      arr.forEach(item => {
        if (item.id == id) {
          result = item
        }
        if (!result && item.children?.length > 0) {
          result = this.recursion(item.children, id)
        }
      })
      return result
    },
    cityVisible(event, applyIndex, key) {
      if (event) return

      const fData = this.queryParams.applyList[applyIndex].trafficList[0]
      let res = this.recursion(this.cityOptions, fData[key + 'Id'])
      if (!res || res.parentId !== '0') return;

      if (key === 'end') {
        this.queryParams.applyList[applyIndex].endAddrId = ''
        this.$set(this.queryParams.applyList[applyIndex].trafficList[0], 'endAddr', '')
        let listNameArr = ['subsidyList', 'stayList', 'otherList']
        listNameArr.forEach(key => {
          this.queryParams.applyList[applyIndex][key].forEach((item, index) => {
            this.$set(this.queryParams.applyList[applyIndex][key][index], 'endAddr', '')
          })
        })
      } else {
        this.queryParams.applyList[applyIndex].startAddrId = ''
        this.$set(this.queryParams.applyList[applyIndex].trafficList[0], 'startAddr', '')
      }
      this.$message.error('不能选择最顶级')
    },
    cityChange(event, applyIndex, key) {
      console.log(event);
      const fData = this.queryParams.applyList[applyIndex].trafficList[0]
      let res = this.recursion(this.cityOptions, event)
      let mn = res.mergerName.split(',')
      mn.shift()
      const areaName = mn.join('/')
      if (key == 'end') {
        if (fData.startAddr === areaName) {
          this.queryParams.applyList[applyIndex].endAddrId = ''
          this.$message.error('出发/到达城市不能重复')
          return
        }
        this.$set(this.queryParams.applyList[applyIndex].trafficList[0], 'endAddr', areaName)
        this.$set(this.queryParams.applyList[applyIndex].trafficList[0], 'endId', res.id)
        let listNameArr = ['subsidyList', 'stayList', 'otherList']
        listNameArr.forEach(key => {
          this.queryParams.applyList[applyIndex][key].forEach((item, index) => {
            this.$set(this.queryParams.applyList[applyIndex][key][index], 'endAddr', areaName)
          })
        })
      } else {
        if (fData.end === areaName) {
          this.queryParams.applyList[applyIndex].startAddrId = ''
          this.$message.error('出发/到达城市不能重复')
          return
        }
        this.$set(this.queryParams.applyList[applyIndex].trafficList[0], 'startAddr', areaName)
        this.$set(this.queryParams.applyList[applyIndex].trafficList[0], 'startId', res.id)
      }
    },
    init() {
      this.transmitParams = this.$route.query ? this.$route.query : null
      this.formDisabled =
        (this.$route.query && this.$route.query.status > 1) ||
          this.$route.query.type == 'finished'
          ? true
          : false
      // 如果id存在获取页面详情
      if (this.transmitParams.id && this.transmitParams.id != 0) {
        // 获取页面详情接口
        this.drawerForm.id = this.transmitParams.id
        getDetailTravel(this.drawerForm.id).then((res) => {
          if (res.data.applyList && res.data.applyList.length > 0) {
            res.data.applyList.forEach((item, index) => {
              this.price.push({
                sum: 0,
                trafficList: { fee: 0 },
                subsidyList: { fee: 0 },
                stayList: { fee: 0 },
                otherList: { fee: 0 },
              })
              this.bill.push({
                sum: 0,
                trafficList: { bill: 0 },
                subsidyList: { bill: 0 },
                stayList: { bill: 0 },
                otherList: { bill: 0 },
              })
              let sum1 = 0
              let total1 = 0
              const arr = ['trafficList', 'subsidyList', 'stayList', 'otherList']
              item.trafficList.forEach(val => {
                if (val.fee) {
                  sum1 += Number(val.fee ? val.fee : 0)
                }
                if (val.invoice) {
                  total1 += Number(val.invoice ? val.invoice : 0)
                }
                this.price[index].trafficList.fee = sum1
                this.bill[index].trafficList.bill = total1
              })
              let sum2 = 0
              let total2 = 0
              item.subsidyList.forEach(val => {
                if (val.fee) {
                  sum2 += Number(val.fee ? val.fee : 0)
                }
                if (val.invoice) {
                  total2 += Number(val.invoice ? val.invoice : 0)
                }
                this.price[index].subsidyList.fee = sum2
                this.bill[index].subsidyList.bill = total2
                val.fileList = []
                val.attachmentIds = []
                if (val.attchmentDto) {
                  val.attchmentDto.forEach(i => {
                    val.fileList.push({
                      name: i.name,
                      id: i.id,
                      url: i.previewUrl,
                      previewUrl: i.previewUrl,
                    })
                    val.attachmentIds.push(i.id)
                  })
                } else {
                  val.fileList = []
                  val.attachmentIds = []
                  val.attchmentDto = []
                  val.noneBtn = false
                }
              })
              let sum3 = 0
              let total3 = 0
              item.stayList.forEach(val => {
                if (val.fee) {
                  sum3 += Number(val.fee ? val.fee : 0)
                }
                if (val.invoice) {
                  total3 += Number(val.invoice ? val.invoice : 0)
                }
                this.price[index].stayList.fee = sum3
                this.bill[index].stayList.bill = total3
                  val.fileList = []
                  val.attachmentIds = []
                  if (val.attchmentDto) {
                    val.attchmentDto.forEach(i => {
                      val.fileList.push({
                        name: i.name,
                        id: i.id,
                        url: i.previewUrl,
                        previewUrl: i.previewUrl,
                      })
                      val.attachmentIds.push(i.id)
                    })
                  } else {
                    val.fileList = []
                    val.attachmentIds = []
                    val.attchmentDto = []
                    val.noneBtn = false
                  }
              })
              let sum4 = 0
              let total4 = 0
              item.otherList.forEach(val => {
                if (val.fee) {
                  sum4 += Number(val.fee ? val.fee : 0)
                }
                if (val.invoice) {
                  total4 += Number(val.invoice ? val.invoice : 0)
                }
                this.price[index].otherList.fee = sum4
                this.bill[index].otherList.bill = total4
                  val.fileList = []
                  val.attachmentIds = []
                  if (val.attchmentDto) {
                    val.attchmentDto.forEach(i => {
                      val.fileList.push({
                        name: i.name,
                        id: i.id,
                        url: i.previewUrl,
                        previewUrl: i.previewUrl
                      })
                      val.attachmentIds.push(i.id)
                    })
                  } else {
                    val.fileList = []
                    val.attachmentIds = []
                    val.attchmentDto = []
                    val.noneBtn = false
                  }
              })

              item.trafficList.forEach(val => {
                val.fileList = []
                val.attachmentIds = []
                if (val.attchmentDto) {
                  val.attchmentDto.forEach(i => {
                    console.log(val.fileList);
                    val.fileList.push({
                      name: i.name,
                      id: i.id,
                      url: i.previewUrl,
                      previewUrl: i.previewUrl
                    })
                    val.attachmentIds.push(i.id)
                  })
                } else {
                  val.fileList = []
                  val.attachmentIds = []
                  val.attchmentDto = []
                  val.noneBtn = false
                }
              });
            })

            const arr = ['trafficList', 'subsidyList', 'stayList', 'otherList']
            console.log(this.price, '================');

            this.price.forEach((val, index) => {
              let sum = 0
              arr.forEach(key => {
                if (val[key].fee) {
                  sum += Number(val[key].fee ? val[key].fee : 0)
                }
              })
              this.price[index].sum = sum
            })

            console.log(this.bill, '++++++++++++')

            this.bill.forEach((val, index) => {
              let total = 0
              arr.forEach(key => {
                if (val[key].bill) {
                  total += Number(val[key].bill ? val[key].bill : 0)
                }
              })
              this.bill[index].sum = total
            })

            res.data.applyList.forEach(item => {
              item.stayList.forEach(val => {
                val.fileList = []
                val.attachmentIds = []
                if (val.attchmentDto) {
                  val.attchmentDto.forEach(i => {
                    val.fileList.push({
                      name: i.name,
                      id: i.id,
                      url: i.previewUrl
                    })
                    val.attachmentIds.push(i.id)
                  })
                } else {
                  val.fileList = []
                  val.attachmentIds = []
                  val.attchmentDto = []
                  val.noneBtn = false
                }
              });
            })
          }
          console.log(res.data, '---=-=-===-');
          this.queryParams = res.data
          this.queryParams.parentCompanyName = this.queryParams.companyName
          // this.title += `（${res.data.instanceNo}）`
          if (this.queryParams.pass == 1) this.getLeftFee(res.data.projectId)
          this.createTime = res.data.createTime
          this.createTime1 = res.data.createTime ? dayjs(res.data.createTime).format('YYYY-MM-DD HH:mm') : dayjs().format('YYYY-MM-DD HH:mm')
          this.$set(this, 'preview', true)
          this.queryParams.attachmentIds = []
          this.fileList = res.data.attchmentDto
          if (this.queryParams.attchmentDto) {
            this.queryParams.attchmentDto.forEach((item) => {
              this.queryParams.attachmentIds.push(item.id)
              item.name = item.name + '.' + item.ext
            })
          }
        })
      } else {
        this.createTime1 = this.createTime ? dayjs(this.createTime).format('YYYY-MM-DD HH:mm') : dayjs().format('YYYY-MM-DD HH:mm')
        this.queryParams.createByName = this.$store.getters.nickName
        this.getLeftFee(this.$store.getters.userId)
        //如果有权限，就设为false
        getInfo(this.$store.getters.token).then(res => {
          if (!this.transmitParams.id || this.transmitParams.id == 0) {
            this.$set(this.queryParams, 'parentCompanyName', res.data.parentCompanyName)
            this.$set(this.queryParams, 'orgName', res.data.orgName)
          }
          const per = res.data.permissions
          if (per.indexOf('hr:flyback:apply') !== -1) {
            this.isFlyPower = false
          }
        })
      }
    },
    // 选择项目后对项目信息进行赋值
    handleForm(val) {
      this.$set(this.queryParams, 'projectName', val.name)
      this.$set(this.queryParams, 'projectId', val.id)
      this.$set(this.queryParams, 'code', val.code)
      this.$set(this.queryParams, 'projectManagerName', val.projectManagerName)
      this.$set(this.queryParams, 'orgName', val.orgName)
      this.$set(this.queryParams, 'leaderName', val.leaderName)
      this.$set(this.queryParams, 'businessLineName', val.businessLineName)
      this.getLeftFee(val.id)
    },
    // 置空buttonType
    changeButtonType() {
      this.buttonType = ''
    },
    inputPayeeAccount(event) {
      let str = this.formatterInteger(event) + ''
      this.$set(this.queryParams, 'payeeAccount', str)
    },
    moneyValidate(event, key) {
      this.$set(this.queryParams, key, this.formatterNumber(event))
    },
    // 限制输入数字   //输入报销金额自动计算单价
    commonPrice(event, value, key, applyIndex) {
      let list = this.queryParams.applyList[applyIndex][key][value.$index]
      if (key == 'trafficList') {
        this.$set(list, 'fee', this.formatterNumber(event))
      } else if (key == 'stayList') {
        this.$set(list, 'fee', this.formatterNumber(event))
        if (list.days > 0) {
          this.$set(list, 'roomFee', (list.fee / list.days).toFixed(2))
        } else {
          this.$set(list, 'roomFee', list.fee)
        }
      } else if (key == 'subsidyList') {
        this.$set(list, 'standard', this.formatterNumber(event))
        this.$set(
          list,
          'fee',
          (
            (Number(list.workDays) + Number(list.workOvertimes)) *
            Number(list.standard)
          ).toFixed(2)
        )
      } else if (key == 'otherList') {
        this.$set(list, 'fee', this.formatterNumber(event))
      }
    },
    commonInvoice(event, value) {
      value.invoice = this.formatterNumber(event)
    },
    chooseType(type, sign, value, key, must, state, applyIndex) {
      this.$set(this, 'must', must == 1 ? true : false)
      let list = this.queryParams.applyList[applyIndex][key][value.$index]
      if (key == 'otherList') {
        if (type) {
          this.$set(list, 'type', type)
        } else {
          this.$set(list, 'type', 1001)
        }
        if (sign) {
          this.$set(list, 'sign', sign)
        } else {
          this.$set(list, 'sign', -1)
        }
      } else if (key == 'trafficList') {
        const trArr = this.queryParams.applyList[applyIndex][key].map(item => item.typeId === '19')
        // if (trArr.length >= 2 && value.row.typeId == 19) {
        //   this.$set(list, 'type', '')
        //   this.$set(list, 'typeId', '')
        //   this.$message.error("差旅-交通费只能一条"); return;
        // }
        this.$set(list, 'state', state)
        if (type) {
          this.$set(list, 'type', type)
        } else {
          this.$set(list, 'type', 1001)
        }
        if (sign) {
          this.$set(list, 'sign', sign)
        } else {
          this.$set(list, 'sign', 2)
        }
      } else if (key == 'stayList') {
        if (type) {
          this.$set(list, 'type', type)
        } else {
          this.$set(list, 'type', 1001)
        }
        if (sign) {
          this.$set(list, 'sign', sign)
        } else {
          this.$set(list, 'sign', 1)
        }
      } else if (key == 'subsidyList') {
        if (type) {
          this.$set(list, 'type', type)
        } else {
          this.$set(list, 'type', 1001)
        }
        if (sign) {
          this.$set(list, 'sign', sign)
        } else {
          this.$set(list, 'sign', 3)
        }
      }
      if (type) {
        this.$set(list, 'type', type)
      }
      // this.$set(list, "sign", sign);
    },
    changeTime(event, value, key, applyIndex) {
      let startData
      if (key == 'trafficList') {
        startData = new Date(value.row.trafficDate)
      } else if (key == 'subsidyList') {
        startData = new Date(value.row.startDate)
      }
      let endDate = new Date(value.row.endDate)
      let list = this.queryParams.applyList[applyIndex][key][value.$index]
      this.$set(list, 'endSign', '')
      this.$set(list, 'enworkDaysdSign', '')
      if (list.standard) {
      }
      if (startData > endDate) {
        this.$message.warning('结束时间不能早于开始时间')
        this.$set(list, 'endDate', '')
      }
    },
    changeInput(event, value, key, applyIndex) {
      let data = this.formatterNumber(event)
      // let ind = data.indexOf('.')
      // if (ind >= 0) {
      //   if (ind + 3 == data.length) {
      //     data = data.slice(0, data.length - 1)
      //   }
      // }
      // if (data == '') {
      //   data = 0
      // }
      let list = this.queryParams.applyList[applyIndex][key][value.$index]
      this.$set(list, 'workOvertimes', data ? data : 0)

      if (list.standard && list.workDays) {
        if (list.workOvertimes) {
          this.$set(
            list,
            'fee',
            (
              (Number(list.workDays) + Number(list.workOvertimes)) *
              Number(list.standard)
            ).toFixed(2)
          )
        } else {
          this.$set(
            list,
            'fee',
            (Number(list.workDays) * Number(list.standard)).toFixed(2)
          )
        }
      }
    },
    //  计算住宿天数
    checkProjectReimbursement(event, value, key, applyIndex) {
      let type = true
      let data
      let list = this.queryParams.applyList[applyIndex][key][value.$index]
      data = value.row
      if (!data.startDate || !data.endDate) {
        return
      }
      if (key == 'stayList') {
        data.startSign = 0
        data.endSign = 0
      }
      if (key == 'trafficList') {
        data.startSign = 0
        data.endSign = 0
      }
      if (key == 'subsidyList') {
        if (data.endSign == null || data.startSign == null) {
          type = false
        }
        // if (this.index2 >= 0) {
        //   if (this.queryParams[key][this.index2].endDate == list.endDate) {
        //     this.$set(list, "endDate", "");
        //     type = false;
        //     this.index2 = -1;
        //   }
        // }
      }
      if (type) {
        checkProjectReimbursement(data).then((res) => {
          if (Number(res.data.allDays) >= 0) {
            if (key != 'trafficList') {
              this.$set(
                list,
                'days',
                res.data.allDays ? res.data.allDays : null
              )
              this.$set(
                list,
                'workDays',
                res.data.workDays ? res.data.workDays : null
              )
              // this.$set(
              //   list,
              //   'workOvertimes',
              //   res.data.workOvertimes ? res.data.workOvertimes : ''
              // )
            }
            if (key == 'subsidyList') {
              if (list.standard) {
                this.$set(
                  list,
                  'fee',
                  (
                    (Number(list.workDays) + Number(list.workOvertimes)) *
                    Number(list.standard)
                  ).toFixed(2)
                )
              }
            }
            if (key == 'stayList') {
              if (data.fee && data.days > 0) {
                this.$set(list, 'roomFee', (data.fee / data.days).toFixed(2))
              } else {
                this.$set(list, 'roomFee', list.fee)
              }
            }
          } else {
            this.$message.warning('结束时间不能早于开始时间')
            list.endDate = ''
          }
        })
      }
    },
    //end

    // 获取剩余备用金
    getLeftFee(projectId) {
      getLockerAmount(projectId).then((res) => {
        this.ifGetLeftFee = true
        this.$set(
          this.queryParams,
          'leftFee',
          res.data ? res.data.toFixed(2) : 0
        )
      })
    },
    // 根据当前登陆人userId获取银行信息
    getBankInformation() {
      getBankInfo(this.$store.getters.userId).then((res) => {
        this.$set(this.queryParams, 'payee', this.$store.getters.nickName)
        if (res.data) {
          this.$set(this.queryParams, 'payeeAccount', res.data.account)
          this.$set(this.queryParams, 'openBank', res.data.bank)
        }
      })
    },
    addApplyClick() {
      // const length = this.queryParams.applyList.length + 1
      const data = JSON.parse(JSON.stringify(this.applyItemObj))
      // console.log(data);
      // data.stayList[0][trafficPhase] = length
      // data.subsidyList[0][trafficPhase] = length
      // data.trafficList[0][trafficPhase] = length
      // data.otherList[0][trafficPhase] = length
      this.queryParams.applyList.push(data)
      this.price.push({
        sum: 0,
        trafficList: { fee: 0 },
        subsidyList: { fee: 0 },
        stayList: { fee: 0 },
        otherList: { fee: 0 },
      })
      this.bill.push({
        sum: 0,
        trafficList: { bill: 0 },
        subsidyList: { bill: 0 },
        stayList: { bill: 0 },
        otherList: { bill: 0 },
      })
    },
    deleteApplyItem(applyIndex) {
      if (this.queryParams.applyList.length == 1) {
        this.$message.error('不可删除最后一条数据')
      } else {
        this.queryParams.applyList.splice(applyIndex, 1)
        this.price.splice(applyIndex, 1)
        this.bill.splice(applyIndex, 1)
        console.log(this.price);

      }
    },
    add(event, index, applyIndex) {
      const trafficPhase = applyIndex + 1;
      if (event == 'stayList') {
        this.queryParams.applyList[applyIndex][event].push({
          trafficPhase:this.queryParams.applyList[applyIndex][event][0].trafficPhase,
          typeId: '18',
          userId: this.$store.getters.userId,
          type: 1001,
          sign: 1,
          endAddr: this.queryParams.applyList[applyIndex][event][0].endAddr,
          fileList: [],
          fileName: '',
          attachmentIds: [],
        })
      } else if (event == 'subsidyList') {
        this.queryParams.applyList[applyIndex][event].push({
          trafficPhase:this.queryParams.applyList[applyIndex][event][0].trafficPhase,
          typeId: '20',
          userId: this.$store.getters.userId,
          type: 1001,
          sign: 3,
          endAddr: this.queryParams.applyList[applyIndex][event][0].endAddr,
          fileList: [],
          fileName: '',
          attachmentIds: [],
        })
      } else if (event == 'trafficList') {
        console.log(this.queryParams.applyList[applyIndex][event]);
        this.queryParams.applyList[applyIndex][event].push({
          trafficPhase:this.queryParams.applyList[applyIndex][event][0].trafficPhase,
          userId: this.$store.getters.userId,
          typeId: '21',
          sign: 4,
          state: 1,
          type: 1001,
          fileList: [],
          fileName: '',
          attachmentIds: [],
          startAddr: this.queryParams.applyList[applyIndex][event][0].startAddr,
          endAddr: this.queryParams.applyList[applyIndex][event][0].endAddr
        })
      } else {
        this.queryParams.applyList[applyIndex][event].push({
          trafficPhase:this.queryParams.applyList[applyIndex][event][0].trafficPhase,
          userId: this.$store.getters.userId,
          endAddr: this.queryParams.applyList[applyIndex][event][0].endAddr,
          fileList: [],
          fileName: '',
          attachmentIds: [],
        })
      }
    },
    pop(event, index, applyIndex) {
      if (this.queryParams.applyList[applyIndex][event].length > 1) {
        this.queryParams.applyList[applyIndex][event] = this.queryParams.applyList[applyIndex][event].filter(
          (item, ind) => ind !== index
        )
        this.sumFee(event, applyIndex)
        this.sumBill(event, applyIndex)
      } else {
        this.$message.error('不可删除最后一条数据')
      }
    },
    // 上传 组件返回
    upLoad(event) {
      this.queryParams.attachmentIds.push(event.id)
      //this.$set(this, 'preview', true)
    },
    // 删除 组件返回
    deleteFile(event, fileList) {
      this.queryParams.attachmentIds = event
      this.fileList = fileList
    },
    // 触发页面提交和保存
    saveSubmit(val, event) {
      this.buttonType = val
      if (val !== 'save' && val !== 'submit') return;
      this.submit(event)
    },
    back() {
      this.$router.go(-1)
    },
    fomrmatDate(date) {
      return date
        ? new Date(Date.parse(date.replace(/-/g, '/'))).getTime()
        : ''
    },
    // subsidyList
    async validateDate() {
      let msg = ''
      let reptArr = []
      this.queryParams.applyList.forEach((child, childIndex) => {
        child.subsidyList.forEach((item, index) => {
          child.subsidyList.forEach((next, nextIndex) => {
            if (
              item.typeId == next.typeId &&
              item.startDate == next.startDate &&
              item.endDate == next.endDate &&
              item.startDate &&
              item.endDate &&
              index != nextIndex
            ) {
              msg = '差旅补助有日期重复'
            }
          })
        })
        child.stayList.forEach((item, index) => {
          child.stayList.forEach((next, nextIndex) => {
            if (
              item.typeId == next.typeId &&
              item.startDate == next.startDate &&
              item.endDate == next.endDate &&
              item.startDate &&
              item.endDate &&
              index != nextIndex
            ) {
              msg = '住宿费有日期重复'
            }
          })
        })
        reptArr.push(child.startAddrId + '-' + child.endAddrId)
      })

      // if(Array.from(new Set(reptArr)).length < reptArr.length){
      //   msg = "差旅交通段不能重复"
      // }
      return msg
    },
    // 提交 queryParams
    async submit(event) {
      if (this.buttonType == 'save') {
        this.save();
        return;
      }
      let msg = await this.validateDate()
      if (msg) {
        this.$message.error(msg)
        return
      }

      this.$refs.queryParams.validate((valid) => {
        if (valid) {
          const cloneQueryParams = JSON.parse(JSON.stringify(this.queryParams))

          cloneQueryParams.applyList.forEach((child, childIndex) => {
            let trafficList = []
            child.trafficList.forEach((item) => {
              if (item.fee) {
                trafficList.push(item)
              }
            })
            child.trafficList = trafficList

            let subsidyList = []
            child.subsidyList.forEach((item) => {
              if (item.fee && this.queryParams.fly == 0) {
                subsidyList.push(item)
              }
            })
            child.subsidyList = subsidyList

            let stayList = []
            child.stayList.forEach((item) => {
              if (item.fee && this.queryParams.fly == 0) {
                stayList.push(item)
              }
            })
            child.stayList = stayList

            let otherList = []
            child.otherList.forEach((item) => {
              if (item.fee && this.queryParams.fly == 0) {
                otherList.push(item)
              }
            })
            child.otherList = otherList
          })

          cloneQueryParams.processIsCommit = true
          event ? (cloneQueryParams.processIsSubmitSendBack = true) : false
          this.$set(
            cloneQueryParams,
            'leftFee',
            cloneQueryParams.leftFee ? cloneQueryParams.leftFee : 0
          )
          console.log(cloneQueryParams);
          if (cloneQueryParams.id) {
            postSaveOrUpdateData(cloneQueryParams).then((res) => {
              if (res.code === 200) {
                this.$message.success('提交成功')
                // this.back()
                this.$router.push({ path: '/person/finish' })
              }
            })
          } else {
            postSaveOrUpdateData(cloneQueryParams).then((res) => {
              if (res.code === 200) {
                this.$message.success('提交成功')
                this.$router.push({ path: '/person/finish' })
                // this.back()
                // this.$router.push({ path: '/expenseControl/ecList' })
              }
            })
          }
        } else {
          this.$message.error('请检查必填项')
        }
      })
    },
    // 保存 queryParams
    save() {
      this.queryParams.processIsCommit = false
      console.log(this.queryParams);
      if (this.queryParams.id) {
        postSaveOrUpdateData(this.queryParams).then((res) => {
          if (res.code === 200) {
            this.$message.success('保存成功')
            this.$router.push({ path: '/person/backlog' })
            // this.back()
          }
        })
      } else {
        postSaveOrUpdateData(this.queryParams).then((res) => {
          if (res.code === 200) {
            this.$message.success('保存成功')
            // this.back()
            this.$router.push({ path: '/person/backlog' })
          }
        })
      }
    },
    handlerPreview() {
      viewTravel(this.queryParams.id).then((res) => {
        if (res.data) {
          this.toPreview(res.data)
        }
      })
    },
    toPreview({ url, id }) {
      let newpage = this.$router.resolve({
        path: '/Expense',
        query: {
          url: this.viewFileUrl + encodeURIComponent(Base64.encode(url)),
          id,
          name: '差旅报销单.xls'
        }
      })
      window.open(newpage.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-select,
::v-deep .el-date-editor--daterange {
  width: 100%;
}

	.disUoloadSty {
		&::v-deep {
			.el-upload {
				display: none!important;
				/* 上传按钮隐藏 */
			}
		}
	}
.subhead {
  border-left: 3px solid #3196fa;
  padding-left: 10px;
  font-size: 16px;
  color: #3196fa;
}

::v-deep .moneyStyle.el-input.is-disabled .el-input__inner {
  // width: 150px;
  color: #ebb30e;
  // display: inline-block;
}

// .autoShowData ::v-deep.el-input-group__append {
//   display: inline-block;
// }

.buttonBox {
  height: 36px;
  line-height: 36px;

  img {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  img:nth-of-type(1) {
    margin-right: 10px;
  }
}

.butt {
  width: 220px;
  height: 50px;
  background-color: #f5f5f5;
  border: 1px dashed #cccccc;
  color: #999999;

  img {
    width: 20px;
    height: 24px;
    margin-right: 30px;
  }
}

::v-deep .showElUpload .el-form-item__content {
  margin-top: 0px;
}

::v-deep .el-button--medium.is-circle {
  padding: 3px;
}

::v-deep .el-upload-list__item-name {
  height: 40px;
  line-height: 36px;
}

::v-deep .upload-demo-echo .el-upload.el-upload--text {
  display: none;
}

.padding20 {
  padding: 20px 10px;
  font-weight: bold;
}

::v-deep ._table {
  .el-form-item {
    display: flex;
    margin-bottom: 0;

    .el-form-item__label {
      width: 0px !important;
    }

    .el-form-item__content {
      margin-left: 0px !important;
    }
  }
}

.previewBox {
  display: flex;
  align-items: center;
  margin-top: 20px;

  .remarkInput {
    width: 600px;
  }

  .viewBox {
    text-align: center;
    margin-left: 20px;
    color: #3196fa;
    padding-top: 10px;
    cursor: pointer;

    i {
      font-size: 24px;
    }
  }
}

.applyList {
  border: 1px solid #eee;
  padding: 0 15px 15px 15px;
  margin-top: 40px;
  padding-bottom: 0px;

  .applyTitleBox {
    border-bottom: 1px solid #eee;
    // width: 100%;
    margin: 0 -15px;
    padding: 12px 15px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #25292C;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
  }

  .applyTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .applySection {
    // border-bottom: 1px solid #eee;
    // margin: 0 -15px;
    // padding: 24px 18px;
    margin-left: 100px;

    .el-form-item {
      margin-bottom: 0px;
    }
  }
}

.addApply {
  width: 122px;
  height: 32px;
  border-radius: 4px;
  border: 1px dashed #0E73EE;
  margin: 22px auto;
  padding: 6px 8px;
  color: #0E73EE;
  display: flex;
  align-items: center;
  cursor: pointer;

  img {
    margin-right: 8px;
  }
}

.applyItem {
  max-height: 7000px;
  transition: all 0.5s;
  padding-bottom: 15px;
}

.applyItemHide {
  max-height: 0px;
  overflow: hidden;
  transition: all 0.5s;
}

.el-icon-s-unfold {
  transition: all 0.5s;
  transform: rotate(0deg)
}

.el-icon-s-unfold-rotate {
  transform: rotate(180deg);
  transition: all 0.5s;
}

.cursor {
  cursor: pointer;
  margin-left: 10px
}

.titleFlex {
  margin-bottom: 0;
}
</style>
<style lang="scss" scoped>
._table {
  margin-bottom: 20px;

  &::v-deep {
    .upload {
      // display: flex;
      // justify-content: center;
      // align-items: center;
    }

    // .el-upload-list {
    //   display: inline-block;
    //   width: 83%;
    //   vertical-align: top;
    // }

    // .el-upload-list__item {
    //   margin: 0;
    // }

    // .el-upload-list__item-name {
    //   height: auto;
    //   line-height: 18px;
    // }
  }
}

>>>.el-icon-close-tip {
  display: none !important;
}
</style>