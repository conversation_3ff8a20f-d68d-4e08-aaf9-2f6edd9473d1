import request from '@/utils/request'

// 获取页面详情
export function getDetail(userId) {
  return request({
    url: `hr/salaryStandardConfig/info/detail/`+ userId,
    method: 'get'
  })
}

// 获取原金额
export function getReconciliation(useId, type) {
  return request({
    url: `/hr/salaryStandardConfig/update/adjust/${useId}/${type}`,
    method: 'get'
  })
}

// 提交
export function postReconciliation(data) {
  return request({
    url: `/hr/salaryStandardConfig/update`,
    method: 'post',
    data: data
  })
}

// 获取提交详情
export function getReconciliationDetail(id) {
  return request({
    url: `/hr/changeSalary/${id}`,
    method: 'get',
  })
}

// 枚举
export function getSalaryChangeTypeEnum() {
  return request({
    url: `/hr/common/getSalaryChangeTypeEnum`,
    method: 'get',
  })
}



