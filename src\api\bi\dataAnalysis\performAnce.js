import request from "@/utils/request";
// 业绩目标值配置 列表
export function gettarList(query) {
  return request({
    url: `/data/projectTargetValueConfig/list`,
    method: "GET",
    params: query,
  });
}
//业绩目标值配置 部门
export function getDivisionList(query) {
  return request({
    url: `/data/projectTargetValueConfig/getDivisionList`,
    method: "GET",
    params: query,
  });
}
//业绩目标值得配置 详情
export function getValueConfigId(id) {
  return request({
    url: `/data/projectTargetValueConfig/${id}`,
    method: "GET",
  });
}
//业绩目标配置 修改
export function projectTargetValueConfig(query) {
  return request({
    url: `/data/projectTargetValueConfig/edit`,
    method: "PUT",
    data: query,
  });
}
// 查询事业部管理指标配置
export function getMonthlyList(data) {
  return request({
    url: `/data/projectManagerValueConfig/list`,
    method: "post",
    data,
  });
}
// 修改管理目标值配置
export function projectManagerValueConfig(query) {
  return request({
    url: `/data/projectManagerValueConfig`,
    method: "PUT",
    data: query,
  });
}

// 获取部门月度考核详情
export function projectManagerValueConfigId(id) {
  return request({
    url: `/data/projectManagerValueConfig/${id}`,
    method: "get",
  });
}

// 新增业绩目标配置
export function addProjectTargetValue(query) {
  return request({
    url: `/data/projectTargetValueConfig/addYear`,
    method: "post",
    data: query,
  });
}
