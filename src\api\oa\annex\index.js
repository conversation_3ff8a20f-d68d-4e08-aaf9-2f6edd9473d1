import request from '@/utils/request'

// selDefaultAnnex, selUploadAnnex, selCancelAnnex
export function selDefaultAnnex(data) {
  return request({
    url: `/administration/computerRentalInfo`,
    method: 'post',
    data: data
  });
}

export function selUploadAnnex(data) {
  return request({
    url: `/administration/computerRentalInfo`,
    method: 'post',
    data: data
  });
}

export function selCancelAnnex(data) {
  return request({
    url: `/administration/computerRentalInfo`,
    method: 'post',
    data: data
  });
}
