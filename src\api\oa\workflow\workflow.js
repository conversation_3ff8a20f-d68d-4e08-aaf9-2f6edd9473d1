import request from '@/utils/request'

// 获取流程图列表
export function getWorkflowList(query) {
  return request({
    url: `/process/model`,
    method: 'get',
    params: query
  })
}

// 保存流程图
export function saveWorkflow(query) {
  return request({
    url: `/process/model`,
    method: 'post',
    data: query
  })
}

// 查看流程图
export function seeWorkflow(id) {
  return request({
    url: `/process/model/${id}`,
    method: 'get',
  })
}

// 删除流程
export function deleteWorkflow(id) {
  return request({
    url: `/process/model/${id}`,
    method: 'DELETE'
  })
}

// 部署流程
export function deployWorkflow(id) {
  return request({
    url: `/process/model/${id}/deploy`,
    method: 'PUT'
  })
}

// 全量部署流程
export function deployAllWorkflow(id) {
  return request({
    url: `/process/model/${id}/deployAll`,
    method: 'PUT'
  })
}
// 更新
export function updateWorkflow(key) {
  return request({
    url: `/process/model/${key}/upVersion`,
    method: 'PUT'
  })
}
// 获取已部署流程列表
export function getDeployList(query) {
  return request({
    url: `/process/deploy`,
    method: 'get',
    params: query
  })
}

// 启用/停用流程
export function openDeployWorkFlow(id) {
  return request({
    url: `/process/deploy/${id}/enable`,
    method: 'PUT'
  })
}

// 查看流程图照片
export function seeDeployWorkFlowImg(id) {
  return request({
    url: `/process/deploy/${id}/image`,
    method: 'get'
  })
}

// 删除已部署流程
export function deleteDeployWorkFlow(id) {
  return request({
    url: `/process/deploy/${id}`,
    method: 'DELETE'
  })
}

// 流程key下拉
export function keyList(data) {
  return request({
    url: `/process/sysFlowChart/list`,
    method: 'post',
    data
  })
}