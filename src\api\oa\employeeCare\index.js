import request from '@/utils/request'

// 查询员工关怀信息列表
export function getEmployeeCareList () {
  return request({
    url: '/system/employeeCare/list',
    method: 'get',
  })
}
// 查询生日关怀员工信息
export function getNoticeBirthday(data) {
  return request({
    url: '/system/employeeCare/getNoticeBirthday',
    method: 'post',
    data
  })
}

// 查询入党纪念日关怀员工信息
export function getNoticeJoinTheParty(data) {
  return request({
    url: '/system/employeeCare/getNoticeJoinTheParty',
    method: 'post',
    data
  })
}
// 查询入会关怀员工信息
export function getNoticeMembership(data) {
  return request({
    url: '/system/employeeCare/getNoticeMembership',
    method: 'post',
    data
  })
}
// 查询退休关怀员工信息
export function getNoticeRetire(data) {
  return request({
    url: '/system/employeeCare/getNoticeRetire',
    method: 'post',
    data
  })
}

// 查询生日关怀员工信息
export function getOrganizational(data) {
  return request({
    url: '/system/organizational/list',
    method: 'post',
    data
  })
}

// 查询默认祝福语
export function getInfo(data) {
  return request({
    url: '/system/employeeCare/getEmployeeCare',
    method: 'post',
    data
  })
}

// 修改祝福语
export function updataCareInfo(data) {
  return request({
    url: '/system/employeeCare',
    method: 'post',
    data
  })
}
