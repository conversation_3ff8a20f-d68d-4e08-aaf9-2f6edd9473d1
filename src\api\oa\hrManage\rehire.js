//退休返聘
import request from '@/utils/request'

// 添加退休返聘
export function addRecord(data) {
  return request({
    url: `/hr/hrRetireRetirementRecord`,
    method: 'post',
    data
  })
}

// 查询返聘人员列表
export function getDetail(data) {
  return request({
    url: `/hr/hrRetireRetirementRecord/list`,
    method: 'post',
    data
  })
}

// 退休查询-人员详细信息
export function getUserInfo(id) {
  return request({
    url: `/hr/hrRetireRetirementRecord/getUserInfo/${id}`,
    method: 'get',
  })
}

// 返聘查询-人员详细信息
export function getRetireInfo(id) {
  return request({
    url: `/hr/hrRetireRetirementRecord/${id}`,
    method: 'get',
  })
}