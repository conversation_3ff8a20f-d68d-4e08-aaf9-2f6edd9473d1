import request from '@/utils/request'

// 查询评分列表
export function listScore(query) {
  return request({
    url: '/oa/score/list',
    method: 'get',
    params: query
  })
}

// 查询评分详细
export function getScore(id) {
  return request({
    url: '/oa/score/' + id,
    method: 'get'
  })
}

// 新增评分
export function addScore(data) {
  return request({
    url: '/oa/score',
    method: 'post',
    data: data
  })
}

// 修改评分
export function updateScore(data) {
  return request({
    url: '/oa/score',
    method: 'put',
    data: data
  })
}

// 删除评分
export function delScore(id) {
  return request({
    url: '/oa/score/' + id,
    method: 'delete'
  })
}
