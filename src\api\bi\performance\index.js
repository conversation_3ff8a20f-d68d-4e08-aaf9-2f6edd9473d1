import request from "@/utils/request";

// 绩效考评查询列表
export function getPerformanceApprovalData(query){
    return request({
        url:'/hr/performanceEvaluation/projectManagerList',
        method:'post',
        data:query
    })
}

// 绩效申诉查询列表
export function getPerformanceAppealData(query){
    return request({
        url:'/hr/performanceEvaluation/appealEvaluationList',
        method:'post',
        data:query
    })
}

// 月度绩效统计列表
export function getPerformanceMonthData(query){
    return request({
        url:'/hr/performanceEvaluation/monThEvaluationList',
        method:'post',
        data:query
    })
}

// 月度绩效统计列表 -- 导出
export function exportPerformanceMonthData(query){
    return request({
        url:'/hr/performanceEvaluation/exportMonThEvaluationList',
        method:'post',
        responseType: 'blob',
        data:query
    })
}

// 绩效系数
export function getPerformanceCoefficient(query){
    return request({
        url:'/hr/performanceCoefficient/list',
        method:'post',
        data:query
    })
}

// 月度绩效统计穿透(人员名单)
export function getMonThEvaluationPersonList(query){
    return request({
        url:'/hr/performanceEvaluation/monThEvaluationPersonList',
        method:'get',
        params:query
    })
}

// 查询事业部等特殊权限的部门列表
export function listOrganizational(query) {
    return request({
        url: '/system/organizational/orgListAuth',
        method: 'post',
        data: query
    })
}
