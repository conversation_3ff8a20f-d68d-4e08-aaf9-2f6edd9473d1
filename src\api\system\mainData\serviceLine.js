import request from "@/utils/request";

// 查询业务线列表
export function getServiceList(query) {
  return request({
    url: "/system/businessLine/list",
    method: "post",
    data: query,
  });
}

// 新增业务线
export function postService(query) {
  return request({
    url: "/system/businessLine",
    method: "post",
    data: query,
  });
}

// 修改业务线
export function putService(query) {
  return request({
    url: "/system/businessLine",
    method: "put",
    data: query,
  });
}

// 查询单条业务线
export function getServiceDetail(id) {
  return request({
    url: `/system/businessLine/info-dto/${id}`,
    method: "get",
  });
}

// 删除业务线
export function deleteService(ids) {
  return request({
    url: `/system/businessLine/${ids}`,
    method: "DELETE",
  });
}
