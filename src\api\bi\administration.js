import request from '@/utils/request'
// 智能行政

//公文管理
export function documents(){
  return request({
    url: `/data/administrativeScreen/documents`,
    method: 'get',
  })
}

//印章管理
export function sealManagement(){
  return request({
    url: `/data/administrativeScreen/sealManagement`,
    method: 'get',
  })
}

//通知公告管理
export function notice(){
  return request({
    url: `/data/administrativeScreen/notice`,
    method: 'get',
  })
}

//文件交换
export function documentFiles(){
  return request({
    url: `/data/administrativeScreen/documentFiles`,
    method: 'get',
  })
}

//文印预约
export function printing(){
  return request({
    url: `/data/administrativeScreen/printing`,
    method: 'get',
  })
}

//会议管理
export function meetingArrangeList(){
  return request({
    url: `/data/logisticsScreen/meetingArrangeList`,
    method: 'get',
  })
}

//来访管理
export function comeToVisit(){
  return request({
    url: `/data/administrativeScreen/comeToVisit`,
    method: 'get',
  })
}

//获取地图经纬度
export function areaInfoList(){
  return request({
    url: `/data/administrativeScreen/areaInfoList`,
    method: 'get',
  })
}

//获取公函快递经纬度
export function letter(){
  return request({
    url: `/data/administrativeScreen/letter`,
    method: 'get',
  })
}