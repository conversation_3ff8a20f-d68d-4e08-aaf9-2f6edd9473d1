import request from '@/utils/request'

//获取项目列表
export function ProjectInfoController(data){
  return request({
    url:'/project/projectInfo/list',
    method:'post',
    data:data
  })
}
//获取项目信息详细信息
export function projectInfo(id) {
	return request({
		url: `/project/projectInfo/${id}`,
		method: 'get'
	});
}
// 获取项目物资采购申请详细信息
export function projectMaterial(id) {
	return request({
		url: `/fee/projectMaterial/${id}`,
		method: 'get'
	});
}
// 修改项目物资采购申请
export function putProjectMaterial(data) {
	return request({
		url: '/fee/projectMaterial',
		method: 'put',
    data:data
	});
}
// 新增项目物资采购申请
export function postProjectMaterial(data) {
	return request({
		url: '/fee/projectMaterial',
		method: 'post',
    data:data
	});
}
