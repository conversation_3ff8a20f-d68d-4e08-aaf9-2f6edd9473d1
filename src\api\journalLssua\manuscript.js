import request from '@/utils/request'

// 分页查询稿件列表
export function listManuscript(query) {
  return request({
    url: '/manuscript/manuscript/list',
    method: 'post',
    data: query
  })
}

export function draftManuscriptList(query){
  return request({
    url: '/manuscript/manuscript/draftList',
    method: 'post',
    data: query
  })
}

// 新增稿件
export function addManuscript(data) {
  return request({
    url: '/manuscript/manuscript',
    method: 'post',
    data: data
  })
}

// 修改稿件
export function editManuscript(data) {
  return request({
    url: '/manuscript/manuscript',
    method: 'put',
    data: data
  })
}

// 删除稿件
export function delManuscript(id) {
  return request({
    url: '/manuscript/manuscript/' + id,
    method: 'delete'
  })
}

// 查询稿件详细
export function getManuscript(id) {
  return request({
    url: '/manuscript/manuscript/' + id,
    method: 'get'
  })
}


