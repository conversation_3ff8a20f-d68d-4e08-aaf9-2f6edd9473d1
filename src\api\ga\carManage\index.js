import request from '@/utils/request'

// 车辆列表
export function carList(data) {
    return request({
        url: `/administration/veVehicleInfo/list`,
        method: 'post',
        data: data
    });
}
// 车辆申请 预约查询 带时间
export function carApplyList(data) {
    return request({
        url: `/administration/veVehicleInfo/idleList`,
        method: 'post',
        data: data
    });
}

// 车辆详情
export function carDetail(data) {
    return request({
        url: `/administration/veVehicleInfo/` + data,
        method: 'get',
    });
}

// 车辆回显信息
export function carFeedBack(data) {
    return request({
        url: `/administration/veVehicleInfo/getData/` + data,
        method: 'get',
    });
}

// 新增编辑车辆信息
export function carAdd(data) {
    return request({
        url: `/administration/veVehicleInfo/`,
        method: 'post',
        data
    });
}

// 有无暂存信息
export function carSaveInfo() {
    return request({
        url: `/administration/veVehicleInfo/staging/`,
        method: 'get',
    });
}

// 车辆
export function carDelete(data) {
    return request({
        url: `/administration/veVehicleInfo/` + data,
        method: 'delete',
    });
}

// ---------------------------------------- 车辆调度 ------------------------------------------------

// 申请列表
export function applyList(data) {
    return request({
        url: `/administration/veVehicleApply/list`,
        method: 'post',
        data: data
    });
}

// 申请详情
export function applyDetail(data) {
    return request({
        url: `/administration/veVehicleApply/` + data,
        method: 'get',
    });
}

// 用车申请
export function applyAdd(data) {
    return request({
        url: `/administration/veVehicleApply/`,
        method: 'post',
        data
    });
}

// ----------------------------------------车辆维修保养 ------------------------------------------------

// 维修列表
export function maintenanceList(data) {
    return request({
        url: `/administration/veVehicleMaintenance/list`,
        method: 'post',
        data: data
    });
}

// 列表删除
export function maintenanceDelete(data) {
    return request({
        url: `/administration/veVehicleMaintenance/` + data,
        method: 'delete',
    });
}

// 新增保养申请
export function applyMaintenanceAdd(data) {
    return request({
        url: `/administration/veVehicleMaintenance/`,
        method: 'post',
        data
    });
}

// 保养详情
export function maintenanceDetail(data) {
    return request({
        url: `/administration/veVehicleMaintenance/` + data,
        method: 'get',
    });
}

// 有无暂存信息
export function maintenanceSaveInfo(data) {
    return request({
        url: `/administration/veVehicleMaintenance/staging/`,
        method: 'get',
    });
}

// ----------------------------------------车辆违章 ------------------------------------------------

// 违章列表
export function violationList(data) {
    return request({
        url: `/administration/veVehicleRegulations/list`,
        method: 'post',
        data: data
    });
}

// 违章列表删除
export function violationDelete(data) {
    return request({
        url: `/administration/veVehicleRegulations/` + data,
        method: 'delete',
    });
}

// 新增违章申请
export function applyViolationAdd(data) {
    return request({
        url: `/administration/veVehicleRegulations/`,
        method: 'post',
        data
    });
}
// 违章申请详情
export function applyViolationDetail(data) {
    return request({
        url: `/administration/veVehicleRegulations/` + data,
        method: 'get',
    });
}
// 有无暂存信息
export function violationSaveInfo(data) {
    return request({
        url: `/administration/veVehicleRegulations/staging/`,
        method: 'get',
    });
}





