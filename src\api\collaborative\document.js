import request from '@/utils/request'

// 协同文档分享
export function collaborativeShare(data) {
  return request({
    url: '/document/collaborative/share',
    method: 'post',
    data
  })
}

export function listShare(query) {
  return request({
    url: '/document/collaborative/shareList',
    method: 'post',
    data: query
  })
}

// 分页查询公文发布列表
export function listCollaborative(query) {
  return request({
    url: '/document/collaborative/list',
    method: 'post',
    data: query
  })
}

// 提交公文发布
export function addCollaborative(data) {
  return request({
    url: '/document/collaborative',
    method: 'post',
    data: data
  })
}

// 修改公文发布
export function updateCollaborative(data) {
  return request({
    url: '/document/collaborative',
    method: 'put',
    data: data
  })
}

// 删除公文发布
export function delCollaborative(id) {
  return request({
    url: '/document/collaborative/' + id,
    method: 'delete'
  })
}

// 查询公文发布详细
export function getCollaborative(id) {
  return request({
    url: '/document/collaborative/' + id,
    method: 'get'
  })
}

// 查询公文发布详细
export function createCollaborative() {
  return request({
    url: '/file/collaborative/create',
    method: 'post'
  })
}


