import request from '@/utils/request'

// 查询人员薪资调整列表
export function listEmployeeSalaryAdj(query) {
  return request({
    url: '/oa/employeeSalaryAdj/list',
    method: 'get',
    params: query
  })
}

// 查询人员薪资调整详细
export function getEmployeeSalaryAdj(salAdjId) {
  return request({
    url: '/oa/employeeSalaryAdj/' + salAdjId,
    method: 'get'
  })
}

// 新增人员薪资调整
export function addEmployeeSalaryAdj(data) {
  return request({
    url: '/oa/employeeSalaryAdj',
    method: 'post',
    data: data
  })
}

// 修改人员薪资调整
export function updateEmployeeSalaryAdj(data) {
  return request({
    url: '/oa/employeeSalaryAdj',
    method: 'put',
    data: data
  })
}

// 删除人员薪资调整
export function delEmployeeSalaryAdj(salAdjId) {
  return request({
    url: '/oa/employeeSalaryAdj/' + salAdjId,
    method: 'delete'
  })
}
