import request from '@/utils/request'

// 分页查询公文模板列表
export function listTemplate(data) {
  return request({
    url: '/document/template/list',
    method: 'post',
    data
  })
}

// 查询公文模板列表
export function allTemplate(query) {
  return request({
    url: '/document/template/all',
    method: 'get',
    params: query
  })
}

// 新增公文模板
export function addTemplate(data) {
  return request({
    url: '/document/template',
    method: 'post',
    data: data
  })
}

// 修改公文模板
export function updateTemplate(data) {
  return request({
    url: '/document/template',
    method: 'put',
    data: data
  })
}

// 删除公文模板
export function delTemplate(id) {
  return request({
    url: '/document/template/' + id,
    method: 'delete'
  })
}

// 查询公文模板详细
export function getTemplate(id) {
  return request({
    url: '/document/template/' + id,
    method: 'get'
  })
}

// 点击模板进行查询ID和data
export function uploadingCopy(data) {
  return request({
    url: '/file/file/uploadingCopy' ,
    method: 'post',
    data
  })
}

// 进行查询个人公文期号预选列表
export function getDocNo(data) {
  return request({
    url: '/document/dmOrganReservation/userList' ,
    method: 'post',
    data
  })
}


// 进行查询已预约的号码
export function searchIssueNos(data) {
  return request({
    url: '/document/dmOrganReservation/searchIssueNos' ,
    method: 'post',
    data
  })
}