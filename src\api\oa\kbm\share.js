import request from '@/utils/request'

// 查询分享列表
export function listShare(query) {
  return request({
    url: '/oa/share/list',
    method: 'get',
    params: query
  })
}

// 查询分享详细
export function getShare(id) {
  return request({
    url: '/oa/share/' + id,
    method: 'get'
  })
}

// 新增分享
export function addShare(data) {
  return request({
    url: '/oa/share',
    method: 'post',
    data: data
  })
}

// 修改分享
export function updateShare(data) {
  return request({
    url: '/oa/share',
    method: 'put',
    data: data
  })
}

// 删除分享
export function delShare(id) {
  return request({
    url: '/oa/share/' + id,
    method: 'delete'
  })
}
