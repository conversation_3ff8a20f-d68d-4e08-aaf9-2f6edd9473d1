import request from "@/utils/request";

// 项目主页 项目列表 --> 工作日志填报
export function getProjectInfoListByDate(data) {
  return request({
    url: "/project/projectInfo/getProjectInfoListByDate",
    method: "post",
    data: data,
  });
}

// 项目主页 项目列表
export function projectList(data) {
  return request({
    url: "/project/projectInfo/list",
    method: "post",
    data: data,
  });
}
// 根据权限获取项目列表
export function getProListByPermissions(data) {
  return request({
    url: "/project/projectInfo/listSelectWithManager",
    method: "post",
    data: data,
  });
}

// 项目主页 项目列表 弹框
export function projectListDialog(data) {
  return request({
    url: "/project/projectInfo/listSelect",
    method: "post",
    data: data,
  });
}
// 获取项目全貌
export function getProjectInfoAll(id) {
  return request({
    url: `/project/projectInfo/getProjectInfoAll/${id}`,
    method: "get",
    params: { listType: 3 },
  });
}
// 获取人员列表
export function baseList(data) {
  return request({
    url: "/system/user/baseList",
    method: "post",
    data: data,
  });
}
// 获取项目列表（全、新）
export function projectListAll(data) {
  return request({
    url: "/project/projectInfo/listAll",
    method: "post",
    data: data,
  });
}
//获取项目信息详细信息
export function projectInfo(id) {
  return request({
    url: `/project/projectInfo/${id}`,
    method: "get",
  });
}

//新增售前项目信息
export function addProject(data) {
  return request({
    url: "/project/presalesProject",
    method: "post",
    data: data,
  });
}
// 修改售前项目\
export function putPresalesProject(data) {
  return request({
    url: "/project/presalesProject",
    method: "put",
    data: data,
  });
}
// 获取售前项目信息
export function getPresalesProject(id) {
  return request({
    url: `/project/presalesProject/${id}`,
    method: "get",
  });
}
// 新增正式项目信息
export function formalProject(data) {
  return request({
    url: "/project/formalProject",
    method: "post",
    data: data,
  });
}
// 获取正式项目详情
export function getFormalProject(id) {
  return request({
    url: `/project/formalProject/${id}`,
    method: "get",
  });
}
// 修改正式项目\
export function putFormalProject(data) {
  return request({
    url: "/project/formalProject",
    method: "put",
    data: data,
  });
}
//查询项目里程碑列表
export function getProjectMilestones(id) {
  return request({
    url: `/project/projectMilestones/${id}`,
    method: "get",
  });
}
// 删除里程碑阶段
export function projectMilestones(id) {
  return request({
    url: `/project/projectMilestones/${id}`,
    method: "DELETE",
  });
}
//查询任务列表
export function projectTaskList(data) {
  return request({
    url: "/project/projectTask/list",
    method: "post",
    data: data,
  });
}
// 删除任务列表
export function deleProjectTask(id) {
  return request({
    url: `/project/projectTask/${id}`,
    method: "DELETE",
  });
}
// 获取里程碑阶段列表
export function getProjectMilestonesList(data) {
  return request({
    url: "/project/projectMilestones/list",
    method: "post",
    data: data,
  });
}
// 根据类型获取里程碑阶段
export function getListByType(data) {
  return request({
    url: "/project/projectTypeMilepost/getListByType",
    method: "post",
    data: data,
  });
}
// 新增项目里程碑
export function addProjectMilestones(data) {
  return request({
    url: "/project/projectMilestones",
    method: "post",
    data: data,
  });
}
// 项目验收单详情
export function projectAcceptance(id) {
  return request({
    url: `/project/projectAcceptance/${id}`,
    method: "get",
  });
}
// 新增项目验收单
export function postProjectAcceptance(data) {
  return request({
    url: "/project/projectAcceptance",
    method: "post",
    data: data,
  });
}
// 修改项目验收单
export function putProjectAcceptance(data) {
  return request({
    url: "/project/projectAcceptance",
    method: "put",
    data: data,
  });
}
// 获取组织列表
export function getOrgList(data) {
  return request({
    url: "/system/organizational/list",
    method: "post",
    data
  });
}
// 查询正式项目信息列表
export function formalProjectList(query) {
  return request({
    url: "/project/formalProject/list",
    method: "get",
    params: query,
  });
}
// 获取客户列表
export function getCustomerInfoList() {
  return request({
    url: "/project/common/getCustomerInfoList",
    method: "get",
  });
}
// 获取客户部门列表
export function getCustomerDepartmentList(id) {
  return request({
    url: "/project/common/getCustomerDepartmentList",
    method: "post",
    data: {
      customerId: id,
    },
  });
}
// 获取客户部门联系人列表
export function getCustomerDepartmentContextList(id) {
  return request({
    url: "/project/common/getCustomerDepartmentContextList",
    method: "get",
    params: {
      deptId: id,
    },
  });
}
// 项目类型数据
export function getProjectTypeEnumData() {
  return request({
    url: "/project/common/getProjectTypeEnum",
    method: "get",
  });
}
// 获取项目等级枚举
export function getProjectLevelEnum() {
  return request({
    url: "/project/common/getProjectLevelEnum",
    method: "get",
  });
}
// 获取合同类型枚举
export function getProjectContractTypeEnum() {
  return request({
    url: "/project/common/getProjectContractTypeEnum",
    method: "get",
  });
}
// 所属业务线
export function getBusinessLineList(data) {
  if(!data){
    data = {};
  }
  return request({
    url: "/project/common/getBusinessLineList",
    method: "post",
    data
  });
}
// 创建新增任务
export function projectTask(data) {
  return request({
    url: "/project/projectTask",
    method: "post",
    data: data,
  });
}
// 修改任务
export function putProjectTask(data) {
  return request({
    url: "/project/projectTask",
    method: "put",
    data: data,
  });
}
// 获取项目任务详细信息
export function getProjectTask(id) {
  return request({
    url: `/project/projectTask/${id}`,
    method: "get",
  });
}

// 新增项目调入人员
export function addPersonRecord(data) {
  return request({
    url: "/project/addPersonRecord",
    method: "post",
    data: data,
  });
}
// 获取项目调入人员详细信息
export function getAddPersonRecord(query) {
  return request({
    url: "/project/addPersonRecord/getDetail",
    method: "get",
    params: query,
  });
}
// 修改项目调入人员
export function putAddPersonRecord(data) {
  return request({
    url: "/project/addPersonRecord",
    method: "put",
    data: data,
  });
}
// 查询项目人员列表
export function projectPersons(data) {
  return request({
    url: "/project/projectPersons/list",
    method: "post",
    data: data,
  });
}
// 新增调出人员列表
export function deletePersonRecord(data) {
  return request({
    url: "/project/deletePersonRecord",
    method: "post",
    data: data,
  });
}
// export function getDeletePersonRecord(id) {
// 	return request({
// 		url: `/project/deletePersonRecord/${id}`,
// 		method: 'get'
// 	});
// }
// 获取项目调出人员详细信息
export function getDeletePersonRecord(query) {
  return request({
    url: "/project/deletePersonRecord/getDetail",
    method: "get",
    params: query,
  });
}
// 修改项目调出人员
export function putDeletePersonRecord(data) {
  return request({
    url: "/project/deletePersonRecord",
    method: "put",
    data: data,
  });
}
// 获取项目状态和阶段状态枚举
export function getPhaseStatusEnum(query) {
  return request({
    url: "/project/common/getProjectStatusEnum",
    method: "get",
    params: query,
  });
}
// 新增项目预算
export function projectBudget(data) {
  return request({
    url: "/project/projectBudget",
    method: "post",
    data: data,
  });
}
// 修改项目里程碑
export function putProjectMilestones(data) {
  return request({
    url: "/project/projectMilestones",
    method: "put",
    data: data,
  });
}
// 获取项目预算详细信息
export function getBudgetInfo(id) {
  return request({
    url: `/project/projectInfo/getBudgetInfo/${id}`,
    method: "get",
  });
}
// 预算金额调整项
export function getProjectBudgetTypeEnumMap() {
  return request({
    url: `/project/common/getProjectBudgetTypeEnumMap`,
    method: "get",
  });
}
// 追加预算，内部项目，追加金额调整项
export function getInnerProjectBudgetTypeEnumMap() {
  return request({
    url: `/project/common/getInnerProjectBudgetTypeEnumMap`,
    method: "get",
  });
}
// 追加预算，当前调整项的已花费金额
export function getUsedPrice(projectId,type) {
  return request({
    url: `/fee/reimbursementType/getPriceByTypeAndProjectId/${type}/${projectId}`,
    method: "get",
  });
}

// 获取项目预算详细信息
export function projectBudgetInfo(id) {
  return request({
    url: `/project/projectBudget/${id}`,
    method: "get",
  });
}
// 获取追加项目预算详细信息
export function projectAddBudgetInfo(id) {
  return request({
    url: `/project/projectAddBudget/${id}`,
    method: "get",
  });
}
// 修改预算详情
export function putProjectBudget(data) {
  return request({
    url: "/project/projectBudget",
    method: "put",
    data: data,
  });
}
// 新增追加项目预算
export function projectAddBudget(data) {
  return request({
    url: "/project/projectAddBudget",
    method: "post",
    data: data,
  });
}
// 修改追加项目预算
export function putProjectAddBudget(data) {
  return request({
    url: "/project/projectAddBudget",
    method: "put",
    data: data,
  });
}
// 获取项目合同信息详细信息
export function getAcceptanceInfo(id) {
  return request({
    url: `/project/projectInfo/getAcceptanceInfo/${id}`,
    method: "get",
  });
}
// 新增项目变更申请
export function projectModify(data) {
  return request({
    url: "/project/projectModify",
    method: "post",
    data: data,
  });
}
// 删除项目变更申请
export function deleteProjectModify(ids) {
  return request({
    url: `/project/projectModify/${ids}`,
    method: "DELETE",
  });
}
// 修改项目变更申请
export function putProjectModify(data) {
  return request({
    url: "/project/projectModify",
    method: "put",
    data: data,
  });
}
// 获取项目变更申请详细信息
export function getProjectModify(id) {
  return request({
    url: `/project/projectModify/${id}`,
    method: "get",
  });
}

// 新增项目周报
export function postWeekReportTaskList(data) {
  return request({
    url: "/project/weekReportTask/list",
    method: "post",
    data: data,
  });
}
// 新增项目周报
export function weekReport(data) {
  return request({
    url: "/project/weekReport",
    method: "post",
    data: data,
  });
}
//根据年月日获取周报
export function weekReportListTime(id, year, month) {
  return request({
    url: `/project/weekReport/${id}/${year}/${month}`,
    method: "get",
  });
}
// 获取周报列表
export function weekReportList(data) {
  return request({
    url: "/project/weekReport/list",
    method: "post",
    data: data,
  });
}
// 获取项目周报详细信息
export function getWeekReport(id) {
  return request({
    url: `/project/weekReport/${id}`,
    method: "get",
  });
}
// 修改周报
export function putWeekReport(data) {
  return request({
    url: "/project/weekReport",
    method: "put",
    data: data,
  });
}
// 查询项目和里程碑阶段列表
export function listWithMilepost() {
  return request({
    url: "/project/projectInfo/listWithMilepost",
    method: "get",
  });
}
// 获取项目里程碑id获取任务列表
export function getTasksByMilepostId(milepostId) {
  return request({
    url: `/project/projectTask/getTasksByMilepostId/${milepostId}`,
    method: "get",
  });
}
// 项目进度列表查询
export function progressList(query) {
  return request({
    url: "/project/projectInfo/progress/list",
    method: "get",
    params: query,
  });
}
// 获取项目进度详细信息
export function progressDetail(id) {
  return request({
    url: `/project/projectInfo/progress/detail/${id}`,
    method: "get",
  });
}
// 获取项目id获取所有里程碑数据信息
export function getMilestoneByProjectId(id) {
  return request({
    url: `/project/projectInfo/getMilestoneByProjectId/${id}`,
    method: "get",
  });
}
// // 查询客户列表
// export function customerInfo() {
//   return request({
//     url: "/system/customerInfo/list",
//     method: "post",
//     data:{}
//   });
// }
// 搜索客户列表
export function searchCustomerInfo(data) {
  return request({
    url: "/system/customerInfo/list",
    method: "post",
    data: data,
  });
}
// 新增客户
export function addCustomerInfo(data) {
  return request({
    url: "/system/customerInfo",
    method: "post",
    data: data,
  });
}
// 修改客户详情
export function putCustomerInfo(data) {
  return request({
    url: "/system/customerInfo",
    method: "put",
    data: data,
  });
}
// 获取客户详情
export function getCustomerInfo(id) {
  return request({
    url: `/system/customerInfo/info-dto/${id}`,
    method: "get",
  });
}
// 删除客户
export function customerListDelete(id) {
  return request({
    url: `/system/customerInfo/${id}`,
    method: "DELETE",
  });
}
// 客户企业性质枚举
export function getCustomerNatureEnum() {
  return request({
    url: "/system/common/getCustomerNatureEnum",
    method: "get",
  });
}
// 获取影响级别枚举
export function getInfluenceLevelEnumEnumMap() {
  return request({
    url: "/project/common/getInfluenceLevelEnumEnumMap",
    method: "get",
  });
}
// 获取项目变更状态枚举
export function getProjectModifyStatusEnumMap() {
  return request({
    url: "/project/common/getProjectModifyStatusEnumMap",
    method: "get",
  });
}
//项目人员变更记录
export function recordChangeData(data) {
  return request({
    url: "/project/projectInfo/getInOutRecord",
    method: "post",
    data: data,
  });
}
// 根据项目主键获取项目健康状态数据
export function getProjectHealthData(id) {
  return request({
    url: `/project/projectInfo/getProjectHealthData/${id}`,
    method: "get",
  });
}

//查询项目变更申请列表
export function projectModifyList(data) {
  return request({
    url: "/project/projectModify/list",
    method: "post",
    data: data,
  });
}

//新增项目结项
export function postProjectClose(data) {
  return request({
    url: "/project/projectClose",
    method: "post",
    data: data,
  });
}
//修改项目结项
export function putProjectClose(data) {
  return request({
    url: "/project/projectClose",
    method: "put",
    data: data,
  });
}
// 获取结项项目交付成果
export function getCloseInfo(id) {
  return request({
    url: `/project/projectInfo/getCloseInfo/${id}`,
    method: "get",
  });
}
//获取项目结项详情
export function getProjectClose(id) {
  return request({
    url: `/project/projectClose/${id}`,
    method: "get",
  });
}
// 问题清单
// 获取问题列表
export function getProjectQuestionList(query) {
  return request({
    url: `/project/projectQuestion/list`,
    method: "get",
    params: query,
  });
}
// 获取问题清单类型枚举
export function getQuestionTypeEnumMap() {
  return request({
    url: `/project/common/getQuestionTypeEnumMap`,
    method: "get",
  });
}
// 获取优先级/发生的概率枚举
export function getRiskProbabilityEnumMap() {
  return request({
    url: `/project/common/getRiskProbabilityEnumMap`,
    method: "get",
  });
}
// 获取问题清单状态枚举
export function getQuestionStatusEnumMap() {
  return request({
    url: `/project/common/getQuestionStatusEnumMap`,
    method: "get",
  });
}
// 获取问题详情信息
export function getProjectQuestion(id) {
  return request({
    url: `/project/projectQuestion/detail/${id}`,
    method: "get",
  });
}
// 保存or修改问题清单
export function postProjectQuestion(data) {
  return request({
    url: `/project/projectQuestion/saveOrUpdate`,
    method: "post",
    data: data,
  });
}
// 删除问题清单
export function deleteProjectQuestion(id) {
  return request({
    url: `/project/projectQuestion/remove/${id}`,
    method: "DELETE",
  });
}
// 风险等级册
// 获取风险状态枚举
export function getRiskStatusEnumMap() {
  return request({
    url: `/project/common/getRiskStatusEnumMap`,
    method: "get",
  });
}
// 获取风险等级册列表
export function getProjectRiskRegister(query) {
  return request({
    url: `/project/projectRiskRegister/list`,
    method: "get",
    params: query,
  });
}
// 获取产生的影响枚举
export function getRiskInfluenceEnumMap() {
  return request({
    url: `/project/common/getRiskInfluenceEnumMap`,
    method: "get",
  });
}
// 风险新增or修改
export function postProjectRiskRegister(data) {
  return request({
    url: `/project/projectRiskRegister/saveOrUpdate`,
    method: "post",
    data: data,
  });
}
// 获取风险详情信息
export function getRiskRegisterDetail(id) {
  return request({
    url: `/project/projectRiskRegister/detail/${id}`,
    method: "get",
  });
}
// 删除风险
export function deleteProjectRiskRegister(id) {
  return request({
    url: `/project/projectRiskRegister/remove/${id}`,
    method: "DELETE",
  });
}
//已办撤销
export function putreversalapi(data) {
  return request({
    url: "/process/revoke/busId",
    method: "POST",
    data,
  });
}

// 客户企业类型

export function getCustomerEnterpriseTypeEnumMap() {
  return request({
    url: `/system/common/getCustomerEnterpriseTypeEnumMap`,
    method: "get",
  });
}

// 终止

export function overProjectById(id) {
  return request({
    url: `/project/projectInfo/overProjectById/${id}`,
    method: "get",
  });
}
// 根据权限查询项目信息列表   项目挂起
export function postHangList(data) {
  return request({
    url: `/project/projectInfo/list`,
    method: "POST",
    data,
  });
}
//新增项目异常信息
export function projectException(data) {
  return request({
    url: `/project/projectException`,
    method: "POST",
    data,
  });
}
//获取项目异常信息
export function getProjectException(id) {
  return request({
    url: `/project/projectException/${id}`,
    method: "get",
  });
}


//获取售前转正式项目的售前预算
export function getInfoByProjectId(projectId) {
  return request({
    url: `/project/projectCost/getInfoByProjectId/${projectId}`,
    method: "get",
  });
}

// 获取项目成本明细
export function getAmountCostDetail(query) {
  return request({
    url: `/project/projectInfo/projectInfo/getAmountCostDetailDto`,
    method: "post",
    data:query
  });
}
// 项目人员成本明细
export function getPersonCostDetail(data) {
  return request({
    url: `/data/projectOverview/selectProjectPersonCost`,
    method: "post",
    data
  });
}
