import request from '@/utils/request'

// 查询参数列表
export function planningList(data) {
  return request({
    url: '/system/planningResourceManagement/list',
    method: 'post',
    data
  })
}

// 查询系统时间列表
export function getSysTimeEnum() {
  return request({
    url: '/system/planningResourceManagement/getSysTimeEnum',
    method: 'get'
  })
}


//新增
export function addPlanning(data) {
  return request({
    url: '/system/planningResourceManagement/add',
    method: 'put',
    data
  })
}

export function switchPlanningStatus(data) {
  return request({
    url: '/system/planningResourceManagement/updateWithoutFile',
    method: 'post',
    data
  })
}

export function updatePlanningSort(data) {
  return request({
    url: '/system/planningResourceManagement/updateSort',
    method: 'post',
    data
  })
}

export function deletePlanning(id) {
  return request({
    url: `/system/planningResourceManagement/delete/${id}`,
    method: 'delete'
  })
}
export function updatePlanning(data) {
  return request({
    url: `/system/planningResourceManagement/update`,
    method: 'post',
    data
  })
}


