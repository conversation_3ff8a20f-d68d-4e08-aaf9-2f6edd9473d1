import request from '@/utils/request'

// 查询登录页背景
export function getLoginImg () {
  return request({
    url: '/file/file/getHomePage',
    method: 'get',
  })
}

// 修改登录页背景
export function updataLoginImg (data) {
  return request({
    url: '/file/file/homePageUploading',
    method: 'post',
    data
  })
}
// 查询
export function getInfos (data) {
  return request({
    url: '/system/sysLogoConfig/getLogoConfig',
    method: 'get',
    data
  })
}
// 保存网站标题
export function saveTitle (data) {
  return request({
    url: '/system/sysLogoConfig/logoUploading',
    method: 'post',
    data,
  })
}