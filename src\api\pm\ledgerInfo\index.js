import request from '@/utils/request'

// 项目台账信息列表
export function ledgerList(data) {
  return request({
    url: `/data/projectAccountInformation/projectAccountInformationList`,
    method: 'POST',
    data: data
  });
}
// 获取项目台账信息详细信息
export function getLedgerInfo(id) {
  return request({
    url: `/data/projectAccountInformation/${id}`,
    method: 'get',
  });
}
// 导出项目台账信息
export function exportProject() {
  return request({
    url: `/data/projectAccountInformation/exportProjectAccountInformation`,
    method: 'get',
    responseType: "blob"
  });
}

// 项目类别字典
export function getProjectTypeMap() {
  return request({
    url: `/data/projectAccountInformation/getProjectTypeMap`,
    method: 'get'
  });
}
