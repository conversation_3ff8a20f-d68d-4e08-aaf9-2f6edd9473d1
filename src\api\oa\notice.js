import request from '@/utils/request'

// 查询公告列表
export function listNotice (data) {
  return request({
    url: '/system/notice/list',
    method: 'post',
    data
  })
}

// 查询通知公告编号获取详细信息引用来点击查看按钮
export function getNoticeLook (id) {
  return request({
    url: '/oa/notice/look/' + id,
    method: 'put'
  })
}

// 查询公告详细
export function getNotice (id) {
  return request({
    url: '/oa/notice/' + id,
    method: 'get'
  })
}


// 修改附件
export function updateFile (data) {
  return request({
    url: '/oa/noticeFile/updateFile',
    method: 'put',
    data: data
  })
}

// 修改rss关键词
export function updateRss (data) {
  return request({
    url: '/oa/notice/Keyword',
    method: 'put',
    data: data
  })
}

//批量添加附件
export function addBasicsFile (data) {
  return request({
    url: '/oa/noticeFile/addBasicsFile',
    method: 'post',
    data: data
  })
}

// 查询首页公告列表
export function indexListNotice (query) {
  return request({
    url: '/oa/notice/indexList',
    method: 'get',
    params: query
  })
}

export function getNoticeType () {
  return request({
    url: '/oa/notice/getNoticeType',
    method: 'get',
  })
}

// 新增公告
export function addNotice (data) {
  return request({
    url: '/oa/notice',
    method: 'post',
    data: data
  })
}

// 修改公告的红点显示
export function updateBadgeNotice (id) {
  return request({
    url: '/oa/notice/editBadge/' + id,
    method: 'put'
  })
}

// 修改公告
export function updateNotice (data) {
  return request({
    url: '/oa/notice/edit',
    method: 'put',
    data: data
  })
}


// 删除公告
export function delNotice (id) {
  return request({
    url: '/oa/notice/' + id,
    method: 'delete'
  })
}
