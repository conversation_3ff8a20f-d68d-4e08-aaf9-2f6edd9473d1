import request from '@/utils/request'

// 分页查询公文发布列表
export function listOfficialDoc(query) {
  return request({
    url: '/document/officialDoc/list',
    method: 'get',
    params: query
  })
}

//公文草稿列表
export function draftListOfficialDoc(query) {
  return request({
    url: '/document/officialDoc/draftList',
    method: 'post',
    data: query
  })
}

// 自动获取公文号
export function autoDocumentNo(organId,organName) {
  return request({
    url: '/document/officialDoc/autoDocumentNo/'+organId+'/'+organName,
    method: 'get'
  })
}

// 提交公文发布
export function addOfficialDoc(data) {
  return request({
    url: '/document/officialDoc',
    method: 'post',
    data: data
  })
}

// 暂存公文发布
export function stagingOfficialDoc(data) {
  return request({
    url: '/document/officialDoc/staging',
    method: 'post',
    data: data
  })
}

// 修改公文发布
export function updateOfficialDoc(data) {
  return request({
    url: '/document/officialDoc',
    method: 'put',
    data: data
  })
}

// 删除公文发布
export function delOfficialDoc(id) {
  return request({
    url: '/document/officialDoc/' + id,
    method: 'delete'
  })
}

// 查询公文发布详细
export function getOfficialDoc(id) {
  return request({
    url: '/document/officialDoc/' + id,
    method: 'get'
  })
}


