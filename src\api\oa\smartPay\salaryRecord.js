import request from '@/utils/request'

// 进行查询薪资包下拉列表
export function getBaseList(data) {
  return request({
    url: '/hr/hrSalaryPackage/baseList',
    method: 'post',
    data
  })
}

// 进行查询薪资档案列表
export function getSalaryList(data) {
  return request({
    url: '/hr/hrSalaryArchives/list',
    method: 'post',
    data
  })
}

// 进行新增用户和薪资包关联
export function addPackageUser(data) {
  return request({
    url: '/hr/hrSalaryArchives/addPackageUser',
    method: 'post',
    data
  })
}

//定薪
// 查询薪资档案的定薪表头
export function getSalaryTitle() {
  return request({
    url: '/hr/hrSalaryArchives/getSalaryTitle',
    method: 'get',
  })
}

// 查询定薪页面信息
export function getSalaryInfo(data) {
  return request({
    url: '/hr/hrSalaryFixing/getInfo',
    method: 'post',
    data
  })
}

// 查询保存/暂存定薪页面信息
export function hrSalaryFixing(data) {
  return request({
    url: '/hr/hrSalaryFixing',
    method: 'post',
    data
  })
}

//调薪
// 查询用户信息接口
export function getSelectUser(id) {
  return request({
    url: `/system/workInfo/salaryByUserId/${id}`,
    method: 'get',
  })
}

// 获取调薪原因
export function getReason() {
  return request({
    url: `/system/dict/data/list?dictType=sys_salary_change_reason`,
    method: 'get',
  })
}

// 薪资项调整--查询薪资包下薪资项列表
export function getPackageTemplate(data) {
  return request({
    url: `/hr/hrSalaryPackage/getPackageTemplate`,
    method: 'post',
    data
  })
}

// 选择薪级下拉列表
export function getlistSalary(data) {
  return request({
    url: `/system/sysPostType/listSalary`,
    method: 'post',
    data
  })
}

// 薪资包调整--查询当前用户的薪资档案的表头(传用户ID)
export function getSalaryUserTitle(id) {
  return request({
    url: `/hr/hrSalaryArchives/getSalaryUserTitle/${id}`,
    method: 'get'
  })
}

// 薪资包调整--当前用户的薪资档案的金额(传用户ID)
export function getSalaryUserAmount(id) {
  return request({
    url: `/hr/hrSalaryArchives/getSalaryUserAmount/${id}`,
    method: 'get'
  })
}

// 薪资包调整--查询薪资包的薪资档案的表头(传薪资包ID)
export function getSalaryPackageTitle(id) {
  return request({
    url: `/hr/hrSalaryArchives/getSalaryPackageTitle/${id}`,
    method: 'get'
  })
}

// 薪资包调整--查询薪资包的薪资档案的金额(传薪资包ID)
export function getSalaryPackageAmount(data) {
  return request({
    url: `/hr/hrSalaryArchives/getSalaryPackageAmount`,
    method: 'post',
    data
  })
}

// 薪资包调整--查询薪资包的薪资档案的金额(传薪资包ID)
export function hrSalaryRaise(data) {
  return request({
    url: `/hr/hrSalaryRaise`,
    method: 'post',
    data
  })
}

// 薪资包调整--查询薪资包的薪资档案的金额(传薪资包ID)
export function getSalaryRaiseInfo(id) {
  return request({
    url: `/hr/hrSalaryRaise/${id}`,
    method: 'get',
  })
}

// 查看列表
// 薪资包调整--查询薪资包的薪资档案的金额(传薪资包ID)
export function getListInfo(data) {
  return request({
    url: `/hr/hrSalaryRaise/list`,
    method: 'post',
    data
  })
}

// 查看列表
// 薪资包调整--查询薪资包的薪资档案的金额(传薪资包ID)
export function getSalaryFixingTitle(data) {
  return request({
    url: `/hr/hrSalaryArchives/getSalaryFixingTitle`,
    method: 'post',
    data
  })
}