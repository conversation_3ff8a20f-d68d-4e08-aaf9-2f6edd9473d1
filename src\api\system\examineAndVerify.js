import request from '@/utils/request'

// 个人信息模块  同意驳回
export function employeeAgree(data) {
    return request({
        url: '/system/user/updateRosterUser',
        method: 'PUT',
        data: data
    })
}
// 教职信息模块  同意驳回
export function teachAgree(data) {
    return request({
        url: '/system/user/updateRosterUserTeaching',
        method: 'PUT',
        data: data
    })
}
// 联系信息模块  同意驳回
export function emergencyAgree(data) {
    return request({
        url: '/system/user/updateRosterUserEmergency',
        method: 'PUT',
        data: data
    })
}
// 家庭信息模块  同意驳回
export function familyAgree(data) {
    return request({
        url: '/system/user/updateRosterUserFamily',
        method: 'PUT',
        data: data
    })
}
// 证件信息模块  同意驳回
export function documentAgree(data) {
    return request({
        url: '/system/user/updateRosterUserDocument',
        method: 'PUT',
        data: data
    })
}
// 个人经历模块  同意驳回
export function UserUnderAgree(data) {
    return request({
        url: '/system/user/updateRosterUserUndergo',
        method: 'PUT',
        data: data
    })
}


