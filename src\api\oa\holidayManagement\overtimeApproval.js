import request from '@/utils/request';

// 新增加班申请
export function postWorkOver(data) {
  return request({
    url: '/hr/userWorkOver',
    method: 'POST',
    data: data
  })
}
// 修改加班申请
export function putWorkOver(data) {
  return request({
    url: '/hr/userWorkOver',
    method: 'PUT',
    data: data
  })
}
// 获取加班申请详情
export function getWorkOverDetail(id) {
  return request({
    url: `/hr/userWorkOver/${id}`,
    method: 'GET',
  })
}
  //验证加班申清是否有重复
  export function getCheckWorkOver(query) {
    return request({
      url: `/hr/userWorkOver/checkRules`,
      method: 'post',
      data: query
    })
}
// 判断日期是否是工作日
export function isWeekday(data) {
  return request({
    url: `/hr/userWorkOver/isWeekday`,
    method: 'post',
    data
  })
}
// 进行加班申请列表查询
export function getWorkOverList(data) {
  return request({
    url: `/hr/userWorkOver/list`,
    method: 'post',
    data
  })
}