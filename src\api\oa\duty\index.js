import request from '@/utils/request'

// 进行查询排班列表信息
export function getDutyList (data) {
  return request({
    url: '/hr/hrUserDuty/list',
    method: 'post',
    data
  })
}
// 进行查询排班列表信息
export function getDutyGroupList (data) {
  return request({
    url: '/hr/hrUserDuty/groupList',
    method: 'post',
    data
  })
}

// 获取排班类型下拉
export function getDutyType () {
  return request({
    url: '/attendance/dutyClasses/searchList',
    // url: '/hr/hrUserDuty/getBeonDutyTypeEnumMap',
    method: 'get',
  })
}

// 进行提交/暂存排班申报
export function addDuty (data) {
  return request({
    url: '/hr/hrUserDuty',
    method: 'post',
    data
  })
}

// 进行查询排班申报信息
export function getDutyInfo (id) {
  return request({
    url: `/hr/hrUserDuty/${id}`,
    method: 'get',
  })
}