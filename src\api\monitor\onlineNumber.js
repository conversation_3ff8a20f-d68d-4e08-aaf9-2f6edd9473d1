import request from '@/utils/request'

// 查询在线用户个数
export function list(query) {
  return request({
    url: '/system/number/list',
    method: 'get',
    params: query
  })
}

// 查询本周在线用户个数
export function weekList() {
  return request({
    url: '/system/number/weekList',
    method: 'get'
  })
}

// 查询本月在线用户个数
export function monthList() {
  return request({
    url: '/system/month/monthList',
    method: 'get'
  })
}


// 根据前端的时间返回数据
export function DateList(start, end) {
  return request({
    url: '/system/number/dateList?dates=' + start +'&dates=' + end,
    method: 'get'
  })
}
