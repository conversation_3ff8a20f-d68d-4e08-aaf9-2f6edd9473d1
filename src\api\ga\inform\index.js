import request from '@/utils/request'

// 查询管理分组集合
export function getInfoList(data) {
  return request({
    url: `/system/groupInfo/list`,
    method: 'post',
    data: data
  });
}

// 新增管理分组
export function addInfo(data) {
  return request({
    url: `/system/groupInfo`,
    method: 'post',
    data: data
  });
}

// 修改管理分组
export function updataInfo(data) {
  return request({
    url: `/system/groupInfo`,
    method: 'put',
    data: data
  });
}
// 删除管理分组
export function delInfo(id) {
  return request({
    url: `/system/groupInfo/${id}`,
    method: 'delete',
  });
}

// 通知公告
// 查询管理分组集合
export function getNoticeList(data) {
  return request({
    url: `/system/notice/list`,
    method: 'post',
    data: data
  });
}

// // 按员工-查询
// export function listUsers(data) {
//   return request({
//     url: `/system/workInfo/listUsers`,
//     method: 'post',
//     data: data
//   });
// }

// 按分组-查询
export function groupList(data) {
  return request({
    url: `/system/groupInfo/list`,
    method: 'post',
    data: data
  });
}

// 按部门-查询
export function orgList(data) {
  return request({
    url: `/system/organizational/orgList`,
    method: 'post',
    data: data
  });
}

// 进行新增通知公告
export function addNotice(data) {
  return request({
    url: `/system/notice`,
    method: 'post',
    data: data
  });
}

// 进行发布通知公告
export function publish(id) {
  return request({
    url: `/system/notice/${id}/publish`,
    method: 'put',
  });
}

// 进行撤回通知公告
export function withdraw(id) {
  return request({
    url: `/system/notice/${id}/withdraw`,
    method: 'put',
  });
}

// 进行查询单个通知公告
export function getNotice(id) {
  return request({
    url: `/system/notice/${id}`,
    method: 'get',
  });
}

// 进行删除通知公告
export function delNotice(id) {
  return request({
    url: `/system/notice/${id}`,
    method: 'delete',
  });
}

// 进行修改通知公告
export function updateNotice(data) {
  return request({
    url: `/system/notice/updateNotice`,
    method: 'post',
    data: data
  });
}

// 进行撤回通知公告
export function read(id) {
  return request({
    url: `/system/notice/${id}/read`,
    method: 'put',
  });
}


