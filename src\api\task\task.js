import request from '@/utils/request'

// 下达任务列表
export function listSend(query) {
  return request({
    url: '/work/taskTaskInfo/initiateList',
    method: 'post',
    data: query
  })
}

export function getDetailByParentId(query) {
  return request({
    url: '/work/taskTaskInfo/initiateListDetail',
    method: 'post',
    data: query
  })
}
// 接收任务列表
export function listReceive(query) {
  return request({
    url: '/work/taskTaskInfo/receiveList',
    method: 'post',
    data: query
  })
}
// 完成任务列表
export function listEnd(query) {
  return request({
    url: '/work/taskTaskInfo/endList',
    method: 'post',
    data: query
  })
}

export function listDuplicate(query) {
  return request({
    url: '/work/taskTaskInfo/deliveryList',
    method: 'post',
    data: query
  })
}

// 查询任务
export function getTask(id) {
  return request({
    url: '/work/taskTaskInfo/' + id,
    method: 'get'
  })
}
// 递归查询任务
export function getTaskById(id) {
  return request({
    url: '/work/taskTaskInfo/getById/' + id,
    method: 'get'
  })
}
// 递归一级查询任务
export function getTaskByIdDetail(id) {
  return request({
    url: '/work/taskTaskInfo/getByIdDetailed/' + id,
    method: 'get'
  })
}

// 新增顶级任务
export function addFirstTask(data) {
  return request({
    url: '/work/taskTaskInfo',
    method: 'post',
    data: data
  })
}
//再次下达任务
export function addTask(data) {
  return request({
    url: '/work/taskTaskInfo/detailed',
    method: 'post',
    data: data
  })
}

// 上报
export function upTask(data) {
  return request({
    url: '/work/taskTaskInfo/report',
    method: 'put',
    data: data
  })
}

//评价
export function evaluateTask(data) {
  return request({
    url: '/work/taskTaskInfo',
    method: 'put',
    data: data
  })
}
// 修改
export function updateTask(data) {
  return request({
    url: '/work/taskTaskInfo/updateDto',
    method: 'put',
    data: data
  })
}

// 删除
export function delTask(id) {
  return request({
    url: '/work/taskTaskInfo/' + id,
    method: 'delete'
  })
}

// 修改任务状态
export function updateTaskStatus(id,type) {
  return request({
    url: '/work/taskTaskInfo/update/'+id,
    method: 'get',
    params: {type}
  })
}

export function endTask(data) {
  return request({
    url: '/work/taskTaskInfo/endTask',
    method: 'put',
    data: data
  })
}

//历史反馈
export function historyFeedback(data) {
  return request({
    url: '/zzy-work/taskTaskFeedback/list',
    method: 'post',
    data: data
  })
}

// 历史评论
export function historyTaskEvaluate(data) {
  return request({
    url: '/zzy-work/taskTaskEvaluate/list',
    method: 'post',
    data: data
  })
}


// 删除附件
export function delFile(id) {
  return request({
    url: '/file/file/removeById/' + id,
    method: 'delete'
  })
}

