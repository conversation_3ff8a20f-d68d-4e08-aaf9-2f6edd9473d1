import request from '@/utils/request'

const client_id = 'web'
const client_secret = '123456'
const scope = 'server'

// 登录方法
export function login(username, password, code, uuid) {
    return request({
        url: '/auth/login',
        method: 'post',
        data: { username, password, code, uuid }
    })
}

// 登录方法
export function loginN(publicToken) {
  return request({
    url: '/auth/loginNew',
    method: 'post',
    data: {publicToken}
  })
}

// 刷新方法
export function refreshToken() {
    return request({
        url: '/auth/refresh',
        method: 'post'
    })
}

// 获取用户详细信息
export function getInfo() {
    return request({
        url: '/system/user/getInfo',
        method: 'get'
    })
}

// 获取缓存信息
export function getCache() {
    return request({
        // url: '/system/dict/data/cache',
        url: '/system/user/cache',
        method: 'get'
    })
}

// 退出方法
export function logout() {
    return request({
        url: '/auth/logout',
        method: 'delete'
    })
}

// 获取验证码
export function getCodeImg() {
    return request({
        url: '/code',
        method: 'get'
    })
}

// 获取员工关怀信息
export function getEmployeeCare(data) {
    return request({
        url: '/system/employeeCare/noticeUserId/' + data,
        method: 'get'
    })
}

// 登录查询个人未读通知公告并弹窗
export function getUserUpList() {
    return request({
        url: '/system/notice/userUpList',
        method: 'get'
    })
}

// 获取员工关怀信息
export function getView(id) {
    return request({
        url: `/msg/notice/${id}/view`,
        method: 'put'
    })
}
