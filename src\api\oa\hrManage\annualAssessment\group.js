import request from '@/utils/request'

// 进行查询管理分组列表
export function getList(data) {
  return request({
    url: `/hr/hrAssessmentTeam/list`,
    method: 'post',
    data
  })
}

// 新增/修改管理分组
export function updataGroup(data) {
  return request({
    url: `/hr/hrAssessmentTeam`,
    method: 'post',
    data
  })
}

// 进行查询管理分组列表
export function getInfo(id) {
  return request({
    url: `/system/notice/${id}`,
    method: 'get',
  })
}
