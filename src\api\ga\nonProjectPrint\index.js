import request from '@/utils/request'

// 非项目用印提交
export function postNonProjectSeal(data) {
	return request({
		url: `/administration/nonProjectSeal`,
		method: 'post',
    data: data
	});
}

// 非项目用印修改
export function putNonProjectSeal(data) {
  return request({
    url: `/administration/nonProjectSeal`,
    method: 'put',
    data: data
  });
}

// 获取项目用印详情
export function getNonProjectSealDetail(id) {
  return request({
    url: `/administration/nonProjectSeal/${id}`,
    method: 'get',
  });
}

