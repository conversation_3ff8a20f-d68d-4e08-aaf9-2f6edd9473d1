import request from "@/utils/request";

// 添加行政采购申请
export function purchaseInfo(data) {
  return request({
    url: `/administration/purchaseInfo`,
    method: "post",
    data: data,
  });
}

// 行政采购申请详情
export function purchaseInfoId(id) {
  return request({
    url: `/administration/purchaseInfo/${id}`,
    method: "get",
  });
}

//行政修改
export function updateByList(data) {
  return request({
    url: `/administration/purchaseDetail/updateByList`,
    method: "post",
    data: data,
  });
}
