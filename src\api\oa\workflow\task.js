import request from '@/utils/request'

// 查询task列表
export function listTask(query) {
  return request({
    url: '/oa/task/list/',
    method: 'get',
    params: query
  })
}

// 查询表单
export function formDataShow(taskID) {
  return request({
    url: '/oa/task/formDataShow/' + taskID,
    method: 'get',
  })
}

// 查询表单
export function formDataSave(taskID, data) {
  return request({
    url: '/oa/task/formDataSave/' + taskID,
    method: 'post',
    data: data
  })
}

// 审批
export function approve(taskID, data) {
  return request({
    url: '/oa/task/' + taskID + '/approve',
    method: 'put',
    data: data
  })
}

// 获取任务日志
export function lookLog(taskID, query) {
  return request({
    url: '/oa/task/' + taskID + '/log',
    method: 'get',
    params: query
  })
}

// 待阅
export function waitRead(id) {
  return request({
    url: `oa/task/${id}/look`,
    method: 'get'
  })
}
