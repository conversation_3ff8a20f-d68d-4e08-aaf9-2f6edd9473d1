import request from '@/utils/request'

//查询草稿箱记录
export function getList(data) {
	return request({
		url: `/document/documentMeetingMinutes/list`,
		method: 'post',
    data: data
	});
}

// 会议纪要模板
// 新增/暂存会议纪要
export function documentMeetingMinutes(data) {
	return request({
		url: `/document/documentMeetingMinutes`,
		method: 'post',
    data: data
	});
}

// 查询单个会议纪要
export function getMeetingInfo(id) {
  return request({
    url: `/document/documentMeetingMinutes/${id}`,
    method: 'get',
  });
}

// 删除单个会议纪要
export function delMeetingInfo(id) {
  return request({
    url: `/document/documentMeetingMinutes/${id}`,
    method: 'delete',
  });
}

// 发布会议纪要
export function publishMeeting(data) {
	return request({
		url: `/document/documentMeetingMinutes/release`,
		method: 'post',
    data: data
	});
}

// 简报模板
// 新增/暂存简报模板
export function documentBriefing(data) {
	return request({
		url: `/document/documentBriefing`,
		method: 'post',
    data: data
	});
}

// 查询单个简报模板
export function getBriefingInfo(id) {
  return request({
    url: `/document/documentBriefing/${id}`,
    method: 'get',
  });
}

// 删除单个简报模板
export function delBriefingInfo(id) {
  return request({
    url: `/document/documentBriefing/${id}`,
    method: 'delete',
  });
}

// 发布简报模板
export function publishBriefing(data) {
	return request({
		url: `/document/documentBriefing/release`,
		method: 'post',
    data: data
	});
}

// 办公室红头模板
// 新增/暂存办公室红头模板
export function documentOfficeRedhead(data) {
	return request({
		url: `/document/documentOfficeRedhead`,
		method: 'post',
    data: data
	});
}

// 查询单个办公室红头模板
export function getOfficeInfo(id) {
  return request({
    url: `/document/documentOfficeRedhead/${id}`,
    method: 'get',
  });
}

// 删除单个办公室红头模板
export function delOfficeInfo(id) {
  return request({
    url: `/document/documentOfficeRedhead/${id}`,
    method: 'delete',
  });
}

// 发布办公室红头模板
export function publishOffice(data) {
	return request({
		url: `/document/documentOfficeRedhead/release`,
		method: 'post',
    data: data
	});
}

// 大红头模板
// 新增/暂存大红头模板
export function documentBigRedhead(data) {
	return request({
		url: `/document/documentBigRedhead`,
		method: 'post',
    data: data
	});
}

// 查询单个大红头模板
export function getBigRedInfo(id) {
  return request({
    url: `/document/documentBigRedhead/${id}`,
    method: 'get',
  });
}

// 删除单个大红头模板
export function delBigRedInfo(id) {
  return request({
    url: `/document/documentBigRedhead/${id}`,
    method: 'delete',
  });
}

// 发布大红头模板
export function publishBigRed(data) {
	return request({
		url: `/document/documentBigRedhead/release`,
		method: 'post',
    data: data
	});
}

// 发文呈批单模板
// 查询缓急类型下拉框接口
export function getUrgencyLevel() {
  return request({
    url: `/system/dict/data/type/urgency_level`,
    method: 'get',
  });
}
// 查询密级类型下拉框接口
export function getSecrecyLevel() {
  return request({
    url: `/system/dict/data/type/secrecy_level`,
    method: 'get',
  });
}
// 新增/暂存发文呈批单
export function documentSubmitDocument(data) {
	return request({
		url: `/document/documentSubmitDocument`,
		method: 'post',
    data: data
	});
}

// 查询单个发文呈批单
export function getSubmitInfo(id) {
  return request({
    url: `/document/documentSubmitDocument/${id}`,
    method: 'get',
  });
}

// 删除单个发文呈批单
export function delSubmitInfo(id) {
  return request({
    url: `/document/documentSubmitDocument/${id}`,
    method: 'delete',
  });
}

// 发布发文呈批单
export function publishSubmit(data) {
	return request({
		url: `/document/documentSubmitDocument/release`,
		method: 'post',
    data: data
	});
}


// 签报单模板
// 新增/暂存签报单模板
export function documentSignReport(data) {
	return request({
		url: `/document/documentSignReport`,
		method: 'post',
    data: data
	});
}

// 查询单个签报单模板
export function getSignInfo(id) {
  return request({
    url: `/document/documentSignReport/${id}`,
    method: 'get',
  });
}

// 删除单个签报单模板
export function delSignInfo(id) {
  return request({
    url: `/document/documentSignReport/${id}`,
    method: 'delete',
  });
}

// 发布签报单模板
export function publishSign(data) {
	return request({
		url: `/document/documentSignReport/release`,
		method: 'post',
    data: data
	});
}

// 小红头模板
// 新增/暂存小红头模板
export function documentSmallRedhead(data) {
	return request({
		url: `/document/documentSmallRedhead`,
		method: 'post',
    data: data
	});
}

// 查询单个小红头模板
export function getSmallRedInfo(id) {
  return request({
    url: `/document/documentSmallRedhead/${id}`,
    method: 'get',
  });
}

// 删除单个小红头模板
export function delSmallRedInfo(id) {
  return request({
    url: `/document/documentSmallRedhead/${id}`,
    method: 'delete',
  });
}

// 发布小红头模板
export function publishSmallRed(data) {
	return request({
		url: `/document/documentSmallRedhead/release`,
		method: 'post',
    data: data
	});
}

// 来电模板
// 新增/暂存来电模板
export function documentIncomingCallRecord(data) {
	return request({
		url: `/document/documentIncomingCallRecord`,
		method: 'post',
    data: data
	});
}

// 查询单个来电模板
export function getIncomingInfo(id) {
  return request({
    url: `/document/documentIncomingCallRecord/${id}`,
    method: 'get',
  });
}

// 删除来电模板
export function delIncomingInfo(id) {
  return request({
    url: `/document/documentIncomingCallRecord/${id}`,
    method: 'delete',
  });
}

// 发布来电模板
export function publishIncoming(data) {
	return request({
		url: `/document/documentIncomingCallRecord/release`,
		method: 'post',
    data: data
	});
}

// 去电模板
// 新增/暂存去电模板
export function documentDeenergizeRecord(data) {
	return request({
		url: `/document/documentDeenergizeRecord`,
		method: 'post',
    data: data
	});
}

// 查询去电模板
export function getDeenergizeInfo(id) {
  return request({
    url: `/document/documentDeenergizeRecord/${id}`,
    method: 'get',
  });
}

// 删除去电模板
export function delDeenergizeInfo(id) {
  return request({
    url: `/document/documentDeenergizeRecord/${id}`,
    method: 'delete',
  });
}

// 发布去电模板
export function publishDeenergize(data) {
	return request({
		url: `/document/documentDeenergizeRecord/release`,
		method: 'post',
    data: data
	});
}

//发文管理
// 查询发文记录
export function getDocumentList(data) {
	return request({
		url: `/document/documentMeetingMinutes/searchDocumentList`,
		method: 'post',
    data: data
	});
}

// 查询去电模板
export function read(id) {
  return request({
    url: `/document/documentUserReceive/isRead/${id}`,
    method: 'get',
  });
}

//文件查看
// 查询公文记录
export function getUserList(data) {
	return request({
		url: `/document/documentUserReceive/searchDocumentUserList`,
		method: 'post',
    data: data
	});
}
// 修改阅读状态为已读
export function updateRead(id) {
	return request({
		url: `/document/documentUserReceive/updateRead/${id}`,
		method: 'get',
	});
}

// 外部公文
// 查询外部公文记录
export function searchDocumentList(data) {
	return request({
		url: `/document/documentExternalDocument/searchDocumentList`,
		method: 'post',
    data: data
	});
}


// 查询发文已读未读人员信息
export function readDocumentList(id) {
	return request({
		url: `/document/documentUserReceive/isRead/${id}`,
		method: 'get',
	});
}

// 新增/暂存外部发文
export function documentExternalDocument(data) {
	return request({
		url: `/document/documentExternalDocument`,
		method: 'post',
    data: data
	});
}

// 查询外部发文
export function getExternalInfo(id) {
  return request({
    url: `/document/documentExternalDocument/${id}`,
    method: 'get',
  });
}

// 删除外部发文
export function delExternalInfo(id) {
  return request({
    url: `/document/documentExternalDocument/${id}`,
    method: 'delete',
  });
}

// 发布外部发文
export function publishExternal(data) {
	return request({
		url: `/document/documentExternalDocument/release`,
		method: 'post',
    data: data
	});
}