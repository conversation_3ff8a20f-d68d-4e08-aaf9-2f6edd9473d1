import request from '@/utils/request'


// 电脑类型
export function computerTypeList(data) {
  return request({
    url: '/system/computerConfigure/computerTypeList',
    method: 'post',
    data: data
  })
}

// 电脑配置列表
export function computerConfigure(data) {
  return request({
    url: '/system/computerConfigure/list',
    method: 'post',
    data: data
  })
}
// 电脑配置列表删除
export function dalete(id) {
  return request({
    url: `/system/computerConfigure/${id}`,
    method: 'delete',
  })
}

// 电脑类型
export function saveOne(data) {
  return request({
    url: '/system/computerConfigure/saveOne',
    method: 'post',
    data: data
  })
}


// 修改
export function update(data) {
  return request({
    url: '/system/computerConfigure/update',
    method: 'put',
    data:data
  })
}

// 修改
export function importComputerConfigure(data) {
  return request({
    url: '/system/computerConfigure/importComputerConfigure',
    method: 'post',
    data:data
  })
}
