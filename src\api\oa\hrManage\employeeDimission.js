import request from '@/utils/request'

// 查询员工
export function getDirectManager(data) {
  return request({
    url: `/system/workInfo/listUsers`,
    method: 'post',
    data:data
  })
}
// 查询带转正员工
export function listUsersStatus(data) {
  return request({
    url: `/system/workInfo/listUsersStatus`,
    method: 'post',
    data:data
  })
}
// 退休查询 除去退休返聘人员
export function listRetireUser(data) {
  return request({
    url: `/system/workInfo/listRetireUsers`,
    method: 'post',
    data:data
  })
}
// 退休查询 退休列表-退休待办
export function retireUsers(data) {
  return request({
    url: `/system/workInfo/retireUsersList`,
    method: 'post',
    data:data
  })
}

// 根据userId查询个人信息
export function getPersonalData(id) {
  return request({
    url: `hr/transfer/tranAndTurn/${id}`,
    method: 'get'
  })
}
// 查询离职原因
export function getLeaveReasons() {
  return request({
    url: '/hr/common/getReasonsForResignationEnum',
    method: 'get'
  })
}

// 提交离职申请
export function postLeaveReasons(query) {
  return request({
    url: '/hr/turnover',
    method: 'post',
    data: query
  })
}

// 获取详情
export function getLeaveReasonsDetail(id) {
  return request({
    url: `/hr/turnover/getUserByPersonChangeId/${id}`,
    method: 'get',
  })
}

// ------------------------------------lx新增接口--------------------------------

// 暂存和提交--------转正申请
export function positiveApply(query) {
  return request({
    url: '/hr/regularDetail/add',
    method: 'post',
    data: query
  })
}
// 暂存和提交--------转正申请---修改
export function editpositiveApply(query) {
  return request({
    url: '/hr/regularDetail/updateById',
    method: 'put',
    data: query
  })
}
// 转正查看详情
export function positiveDetail(query) {
  return request({
    url: '/hr/regularDetail/getByUserId/' + query,
    method: 'get',
  })
}

// 转正查看详情
export function positiveDetailId(query) {
  return request({
    url: '/hr/regularDetail/' + query,
    method: 'get',
  })
}


// 暂存和提交--------离职申请
export function newDeparture(query) {
  return request({
    url: '/hr/outDetail/add',
    method: 'post',
    data: query
  })
}
// 离职-----交接人列表
export function handoverList(query) {
  return request({
    url: '/system/workInfo/findRegularUsers',
    method: 'post',
    data: query
  })
}
// 离职查看详情
export function detailDimission(query) {
  return request({
    url: '/hr/outDetail/depart/'+ query,
    method: 'get',
  })
}
// 用户信息
export function userInfo(userId) {
  return request({
    url: `/system/workInfo/getInfoByUserId/${userId}`,
    method: 'get',
  })
}
// 离职信息  离职
export function flowDetailOut(id) {
  return request({
    url: `/hr/outDetail/${id}`,
    method: 'get',
  })
}

// 流程详情  转正
export function flowDetailReg(id) {
  return request({
    url: `/hr/regularDetail/getById/${id}`,
    method: 'post',
  })
}
// 获取  劳动合同日期
export function workDate(userId) {
  return request({
    url: `/system/contractInfo/findByUserId?userId=${userId}`,
    method: 'get',
  })
}

export function editDimission(query) {
  return request({
    url: '/hr/outDetail/update',
    method: 'put',
    data: query
  })
}

// 获取人员状态
export function getUserStatus(userId) {
  return request({
    url: `/system/user/getUserById/${userId}`,
    method: 'get',
  })
}
//离职列表
export function getEmployeeList(data) {
  return request({
    url: `/hr/outDetail/userLeaveRecordList`,
    method: 'post',
    data:data
  })
}
//转正列表
export function getNormalEmployeeList(data) {
  return request({
    url: `/system/user/userNormalRecordList`,
    method: 'post',
    data:data
  })
}


