import request from '@/utils/request'

// 商机状态数据
export function opportunityStatusData() {
	return request({
		url: '/project/common/getOpportunityStatusEnum',
		method: 'get'
	});
}

// 当前阶段数据
export function currentStageData() {
	return request({
		url: '/project/common/getCurrentStageEnum',
		method: 'get'
	});
}

// 项目类型数据
export function getProjectTypeEnumData() {
	return request({
		url: '/project/common/getProjectTypeEnum',
		method: 'get'
	});
}

// 商机列表数据
export function listBusinessOpportunity(data) {
	return request({
	    url: '/project/businessOpportunity/list?pageNum=' + data.pageNum + '&pageSize=' + data.pageSize,
	    method: 'post',
	    data: data
	})
}


// 添加商机信息
export function opportunitySubmit(data) {
	return request({
		url: '/project/businessOpportunity',
		method: 'post',
		data: data
	})
}

// 修改商机信息
export function putOpportunitySubmit(data) {
	return request({
		url: '/project/businessOpportunity',
		method: 'put',
		data: data
	})
}

// 跟进内容及计划获取商机详细信息
export function opportunityDetail(id) {
	return request({
		url: '/project/businessOpportunity/' + id,
		method: 'get',
	})
}

//获取商机跟进内容列表
export function bsContentList(id) {
	return request({
		url: '/project/businessContent/list',
		method: 'get',
		params: {
			businessId: id
		}
	})
}

//商机跟进内容提交
export function contentSubmit(data) {
	return request({
		url: '/project/businessContent',
		method: 'post',
		data: data
	})
}

//商机跟进内容删除
export function contentDel(id) {
	return request({
		url: '/project/businessContent/delete/' + id,
		method: 'delete',
	})
}

//获取商机跟进计划列表
export function bsPlanList(id) {
	return request({
		url: '/project/businessPlan/list',
		method: 'post',
		data: {
			businessId: id
		}
	})
}

//商机跟进计划提交
export function planSubmit(data) {
	return request({
		url: '/project/businessPlan',
		method: 'post',
		data: data
	})
}

//商机跟进计划删除
export function planDel(id) {
	return request({
		url: '/project/businessPlan/delete/' + id,
		method: 'delete'
	})
}

//客户名称及客户联系人
export function customernameData() {
	return request({
		url: '/project/common/getCustomerInfoList',
		method: 'get',
	})
}

//2.0
//新增计划保存
export function postPlanSave(data) {
	return request({
		url: '/project/businessPlan',
		method: 'post',
		data: data
	})
}
//修改计划保存
export function putPlanSave(data) {
	return request({
		url: '/project/businessPlan',
		method: 'put',
		data: data
	})
}
//计划删除
export function delPlanList(id) {
	return request({
		url: '/project/businessPlan/delete/' + id,
		method: 'delete'
	})
}
//新增内容
export function postContentSave(data) {
	return request({
		url: '/project/businessContent',
		method: 'post',
		data: data
	})
}
//修改内容
export function putContentSave(data) {
	return request({
		url: '/project/businessContent',
		method: 'put',
		data: data
	})
}
//文件删除
export function removeFile(id){
	return request({
	  url: `/file/file/removeById/${id}`,
	  method: 'delete',
	})
}
