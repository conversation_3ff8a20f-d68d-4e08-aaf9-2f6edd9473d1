import request from '@/utils/request'

// 资产列表
export function meansList(data) {
    return request({
        url: `/shed/shedStoreroom/list`,
        method: 'post',
        data: data
    });
}

// 新增资产
export function addMeans(data) {
    return request({
        url: '/shed/shedStoreroom',
        method: 'post',
        data: data
    })
}

// 修改资产
export function updateMeans(data) {
    return request({
        url: '/shed/shedStoreroom',
        method: 'put',
        data: data
    })
}

// 删除资产
export function delMeans(id) {
    return request({
        url: '/shed/shedStoreroom/' + id,
        method: 'delete'
    })
}
// ------------------------------ 入库列表 ---------------------------


// 库存列表信息
export function inQuery(data) {
    return request({
        url: '/shed/shedStoreroomDetail/list',
        method: 'post',
        data: data
    })
}

// 修改库存信息 启用禁用
export function updateStates(data) {
    return request({
        url: '/shed/shedStoreroom',
        method: 'put',
        data: data
    })
}

// 入库操作
export function addInventory(data) {
    return request({
        url: '/shed/shedEnterStoreroom',
        method: 'post',
        data: data
    })
}

// 出库操作
export function leaveInventory(data) {
    return request({
        url: '/shed/shedLeaveStoreroom',
        method: 'post',
        data: data
    })
}

// 查看某项资产入库记录
export function enterRecord(data) {
    return request({
        url: '/shed/shedEnterStoreroom/list',
        method: 'post',
        data: data
    })
}

// 查看某项资产出库记录
export function leaveRecord(data) {
    return request({
        url: '/shed/shedLeaveStoreroom/list',
        method: 'post',
        data: data
    })
}

// 查看某入库记录明细
export function enterDetail(data) {
    return request({
        url: '/shed/shedEnterStoreroom/getInfo',
        method: 'post',
        data: data
    })
}

// 查看某出库记录明细
export function leaveDetail(data) {
    return request({
        url: '/shed/shedLeaveStoreroom/' + data,
        method: 'get',
    })
}

// ------------------------------ 申请办公用品 ---------------------------


// 申请
export function applyMeans(data) {
    return request({
        url: '/shed/shedStoreroomApply/',
        method: 'post',
        data
    })
}

// ------------------------------ 领用列表 ---------------------------


// 领用列表信息
export function consumeList(data) {
    return request({
        url: '/shed/shedUseReturn/list',
        method: 'post',
        data: data
    })
}

// 归还操作
export function returnOperate(data) {
    return request({
        url: '/shed/shedUseReturn/repay',
        method: 'post',
        data: data
    })
}

// 领用操作
export function consumeOperate(data) {
    return request({
        url: '/shed/shedUseReturn/use',
        method: 'post',
        data: data
    })
}

// 查看某资产 归还记录明细
export function consumeEnterDetail(data) {
    return request({
        url: '/shed/shedUseReturn/useList',
        method: 'post',
        data: data
    })
}

// 查看某资产 领用记录明细
export function consumeLeaveDetail(data) {
    return request({
        url: '/shed/shedUseReturn/repayList',
        method: 'post',
        data: data
    })
}

// 查看某资产 领用记录明细
export function userInfo(data) {
    return request({
        url: '/system/user/detail/'+data,
        method: 'get',
    })
}

// 查看某资产 领用详情 暂存之后点击待办查看详情
export function formInfo(data) {
    return request({
        url: '/shed/shedStoreroomApply/'+data,
        method: 'get',
    })
}

// 下载模板
export function downTemplate() {
    return request({
        url: '/shed/shedStoreroomDetail/downloadTemplate',
        method: 'get',
        responseType: 'blob',
    })
}

// ------------------------------ 明细列表 ---------------------------


// 明细列表信息
export function detailList(data) {
    return request({
        url: '/shed/shedStoreroomDetail/propertyList',
        method: 'post',
        data: data
    })
}
// 资产生命周期明细
export function meansDetailList(data) {
    return request({
        url: '/shed/shedStoreroomDetailLog/list',
        method: 'post',
        data: data
    })
}