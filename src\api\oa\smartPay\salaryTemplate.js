import request from '@/utils/request'

// 进行查询薪资模板列表
export function getList(data) {
  return request({
    url: '/hr/hrSalaryTemplate/list',
    method: 'post',
    data
  })
}

// 新增/修改薪资模板
export function hrSalaryTemplate(data) {
  return request({
    url: '/hr/hrSalaryTemplate',
    method: 'post',
    data
  })
}

// 进行启用/禁用模板信息
export function updateEnable(data) {
  return request({
    url: '/hr/hrSalaryTemplate/updateEnable',
    method: 'post',
    data
  })
}

// 进行启用/禁用模板信息
export function deleteTemplate(data) {
  return request({
    url: '/hr/hrSalaryTemplate/deleteTemplate',
    method: 'post',
    data
  })
}

// 查询薪资模板设置
export function salaryList(data) {
  return request({
    url: '/system/dict/data/salaryList',
    method: 'post',
    data
  })
}

// 新增薪资模板设置信息
export function hrSalaryTemplateConfig(data) {
  return request({
    url: '/hr/hrSalaryTemplateConfig',
    method: 'post',
    data
  })
}

// 查询单个薪资模板设置信息
export function getTemplateConfigInfo(id) {
  return request({
    url: `/hr/hrSalaryTemplateConfig/${id}`,
    method: 'get',
  })
}


// 查询工资项属性
export function getSalaryType() {
  return request({
    url: `/system/dict/data/list?dictType=sys_salary_type`,
    method: 'get',
  })
}

// 民族下拉
export function getNationalityEnum() {
  return request({
    url:`/hr/hrSalaryTemplateConfig/getNationalityEnum`,
    method:'get'
  })
}

// 职级下拉
export function getPostList(data) {
  return request({
    url:`/system/sysPostType/list`,
    method:'post',
    data
  })
}

// 环线下拉
export function getDutyEnum() {
  return request({
    url:`/hr/hrSalaryTemplateConfig/getDutyEnum`,
    method:'get'
  })
}

// 考勤下拉
export function getLoopLineEnum() {
  return request({
    url:`/hr/hrSalaryTemplateConfig/getLoopLineEnum`,
    method:'get'
  })
}