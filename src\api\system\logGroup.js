import request from '@/utils/request'

// 查询群组信息
export function getGroupInfo(data){
    return request({
        url:'/system/groupInfo/list',
        method:'post',
        data:data
    })
}
// 新增群组信息
export function addGroup(data) {
    return request({
        url: '/system/groupInfo',
        method: 'post',
        data: data
    })
}
  
// 修改群组
export function updateGroup(data) {
    return request({
        url: '/system/groupInfo',
        method: 'put',
        data: data
    })
}

// 根据群组编号获取详细信息
export function getGroup(groupId) {
    return request({
        url: '/system/groupInfo/' + groupId,
        method: 'post',
    })
}

// 删除群组
export function delGroup(id) { 
    return request({
        url: '/system/groupInfo/' + id,
        method: 'delete'
    })
}

// 根据群组id查询所属用户id集合
export function getGroupUser(groupId) {
    return request({
        url: '/system/groupInfo/groupInfo/getUsersByGroupId',
        method: 'post',
        params:{groupId:groupId}
    })
}

// 新增群组成员
export function addGroupUser(data) {
    return request({
        url: '/system/groupInfo/addUser',
        method: 'post',
        data:data
    })
}

// 查询评论列表
export function getCommentInfo(data){
    return request({
        url:'/project/projectDailyRecordComment/list',
        method:'post',
        data:data
    })
}

// 删除评论
export function delComment(id) { 
    return request({
        url: '/project/projectDailyRecordComment/' + id,
        method: 'delete'
    })
}
  