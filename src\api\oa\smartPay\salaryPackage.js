import request from '@/utils/request'

// 进行查询薪资包列表
export function getPackageList(data) {
  return request({
    url: '/hr/hrSalaryPackage/list',
    method: 'post',
    data
  })
}

// 进行查询薪资模板列表
export function getSalaryTemplateList(data) {
  return request({
    url: '/hr/hrSalaryTemplate/packageList',
    method: 'post',
    data
  })
}

// 进行查询薪资模板列表
export function getTree() {
  return request({
    url: '/system/dict/data/list?dictType=sys_staff_type',
    method: 'get',
  })
}

// 获取员工类型下的员工信息
export function listPackageUsers(data) {
  return request({
    url: '/system/workInfo/listPackageUsers',
    method: 'post',
    data
  })
}

// 进行新增/修改薪资包设置
export function hrSalaryPackage(data) {
  return request({
    url: '/hr/hrSalaryPackage',
    method: 'post',
    data
  })
}

// 进行新增/修改薪资包设置
export function getInfo(data) {
  return request({
    url: '/hr/hrSalaryPackage/getInfo',
    method: 'post',
    data
  })
}

// 进行新增/修改薪资包设置
export function deleteSalaryPackage(data) {
  return request({
    url: '/hr/hrSalaryPackage/deleteSalaryPackage',
    method: 'post',
    data
  })
}