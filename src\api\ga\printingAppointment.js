import request from '@/utils/request'

// 进行提交/暂存公函快递
export function documentPrintingAppointment(data) {
  return request({
    url: `/document/documentPrintingAppointment`,
    method: 'post',
    data: data
  });
}

//  进行查询公函快递
export function getPrintingAppointmentInfo(id) {
  return request({
    url: `/document/documentPrintingAppointment/${id}`,
    method: 'get',
  });
}

//  进行查询公函快递
export function getPrintingInfo(data) {
  return request({
    url: `/document/documentPrintingAppointment/getInfo`,
    method: 'post',
    data
  });
}

// 进行查询文印预约列表
export function getList(data) {
  return request({
    url: `/document/documentPrintingAppointmentDetail/list`,
    method: 'post',
    data: data
  });
}

// 进行修改文印预约打印状态
export function printUpdate(data) {
  return request({
    url: `/document/documentPrintingAppointmentDetail/print`,
    method: 'post',
    data: data
  });
}