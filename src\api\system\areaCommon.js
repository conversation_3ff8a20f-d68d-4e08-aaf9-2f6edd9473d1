import request from '@/utils/request'

// 查询常用地区列表
export function listCommon(query) {
  return request({
    url: '/system/areaCommon/list',
    method: 'post',
    data: query
  })
}

// 查询常用地区详细
export function getCommon(id) {
  return request({
    url: '/system/areaCommon/' + id,
    method: 'get'
  })
}

// 新增常用地区
export function addCommon(data) {
  return request({
    url: '/system/areaCommon',
    method: 'post',
    data: data
  })
}

// 修改常用地区
export function updateCommon(data) {
  return request({
    url: '/system/areaCommon',
    method: 'put',
    data: data
  })
}

// 删除常用地区
export function delCommon(id) {
  return request({
    url: '/system/areaCommon/' + id,
    method: 'delete'
  })
}
