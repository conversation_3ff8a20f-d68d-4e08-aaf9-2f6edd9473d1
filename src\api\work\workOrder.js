import request from '@/utils/request'

// 分页查询工单列表
export function listWorkOrder(query) {
  return request({
    url: '/work/workOrder/list',
    method: 'post',
    data: query
  })
}

// 查询工单详细
export function getWorkOrder(id) {
  return request({
    url: '/work/workOrder/' + id,
    method: 'get'
  })
}

// 提交工单
export function addWorkOrder(data) {
  return request({
    url: '/work/workOrder',
    method: 'post',
    data: data
  })
}

// 修改工单
export function editWorkOrder(data) {
  return request({
    url: '/work/workOrder',
    method: 'put',
    data: data
  })
}

// 删除工单
export function delWorkOrder(id) {
  return request({
    url: '/work/workOrder/' + id,
    method: 'delete'
  })
}


// 分页查询协作单列表
export function listWorkCooperation(query) {
  return request({
    url: '/work/wkWorkCooperation/list',
    method: 'post',
    data: query
  })
}
// 查询协作单详细
export function getWorkCooperation(id) {
  return request({
    url: '/work/wkWorkCooperation/' + id,
    method: 'get'
  })
}

// 提交协作单
export function addWorkCooperation(data) {
  return request({
    url: '/work/wkWorkCooperation',
    method: 'post',
    data: data
  })
}

// 修改协作单
export function editWorkCooperation(data) {
  return request({
    url: '/work/wkWorkCooperation',
    method: 'put',
    data: data
  })
}

// 删除协作单
export function delWorkCooperation(id) {
  return request({
    url: '/work/wkWorkCooperation/' + id,
    method: 'delete'
  })
}





// 分页查询公文发布列表
export function myWorkOrder(query) {
  return request({
    url: '/work/workOrder/myList',
    method: 'post',
    data: query
  })
}

export function getCheckWorkOrder(id) {
  return request({
    url: '/work/workCheck/work/' + id,
    method: 'get'
  })
}

export function checkWorkOrder(query) {
  return request({
    url: '/work/workCheck/',
    method: 'post',
    data: query
  })
}

export function myWorkCooperation(query) {
  return request({
    url: '/work/wkWorkCooperation/myList',
    method: 'post',
    data: query
  })
}

export function getCheckWorkCooperation(id) {
  return request({
    url: '/work/workCheck/cooperation/' + id,
    method: 'get'
  })
}

export function checkWorkCooperation(query) {
  return request({
    url: '/work/workCheck/',
    method: 'post',
    data: query
  })
}






