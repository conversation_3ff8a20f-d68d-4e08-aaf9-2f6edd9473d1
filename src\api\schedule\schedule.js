import request from '@/utils/request'

// 查询用户部门及以下所有人员
export function orgUserList(data) {
    return request({
        url: '/system/workInfo/queryUserOrgNoRetireList/' + data,
        method: 'get',
    })
}
// ------------------------------------------标签-------------------------------------
// 日程管理标签列表
export function labelList(data) {
    return request({
        url: '/itinerary/myItineraryLabel/list',
        method: 'post',
        data: data,
        headers:{ 'Content-Type': 'application/json;charset=utf-8' }
    })
}

// 新增日程管理标签
export function addLabel(data) {
    return request({
        url: '/itinerary/myItineraryLabel',
        method: 'post',
        data: data
    })
}
// 修改
export function updateLabel(data) {
    return request({
        url: '/itinerary/myItineraryLabel',
        method: 'put',
        data: data
    })
}

// 删除
export function delLabel(id) {
    return request({
        url: '/itinerary/myItineraryLabel/' + id,
        method: 'delete'
    })
}

// ------------------------------------------日程-------------------------------------


// 日程列表
export function planList(data) {
    return request({
        url: '/itinerary/myItinerary/list',
        method: 'post',
        data: data
    })
}
// 工作台日程列表 小蓝点
export function planLists(data) {
    return request({
        url: '/itinerary/myItinerary/sameWeekMounth',
        method: 'post',
        data: data
    })
}
// 新增日程
export function addPlan(data) {
    return request({
        url: '/itinerary/myItinerary',
        method: 'post',
        data: data
    })
}
// 修改
export function updatePlan(data) {
    return request({
        url: '/itinerary/myItinerary',
        method: 'put',
        data: data
    })
}

// 删除
export function delPlan(id) {
    return request({
        url: '/itinerary/myItinerary/' + id,
        method: 'delete'
    })
}
// 日程详情
export function planDetails(data) {
    return request({
        url: '/itinerary/myItinerary/' + data,
        method: 'get'
    })
}
// 黄历
export function myItineraryAlmanac(data) {
  return request({
      url: '/itinerary/myItinerary/almanac/get?date=' + data,
      method: 'get'
  })
}
// 某日日程列表详情
export function dateDetail(data) {
    return request({
        url: '/itinerary/myItinerary/sameDay',
        method: 'post',
        data
    })
}
// 某周日程列表详情
export function dateWeekDetail(data) {
    return request({
        url: '/itinerary/myItinerary/sameWeek',
        method: 'post',
        data
    })
}