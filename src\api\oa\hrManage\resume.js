import request from '@/utils/request'

// 查询简历列表
export function listResume(data) {
    return request({
        url: '/hr/hrRecruitUser/list',
        method: 'post',
        data
    })
}

// 查询个人详情
export function employeeInfo(data) {
    return request({
        url: '/hr/hrRecruitUser/' + data,
        method: 'get',
        data
    })
}

// 上传照片
export function updateAvatar(data) {
    return request({
        url: '/hr/hrRecruitUser/updateAvatar',
        method: 'post',
        data
    })
}

// 下载模板
export function downloadDoc() {
    return request({
        url: '/hr/hrRecruitUser/download/template',
        method: 'get',
        responseType: 'blob',
    })
}