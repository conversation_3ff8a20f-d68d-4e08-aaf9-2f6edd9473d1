import request from '@/utils/request'

// 获取快捷菜单
export function listMenu() {
    return request({
        url: '/system/menu/getMenuShortcut',
        method: 'get',
    })
}
// 添加快捷菜单
export function addMenu(data) {
    return request({
        url: '/system/sysUserMenuShortcut',
        method: 'post',
        data
    })
}

// 快捷菜单排序
export function updateSort(data) {
    return request({
        url: '/system/sysUserMenuShortcut/updateSort',
        method: 'post',
        data
    })
}

// 查询快捷菜单
export function searchMenu(data) {
    return request({
        url: '/system/sysUserMenuShortcut/list',
        method: 'post',
        data
    })
}

// 删除快捷入口
export function deleteMenu(data) {
    return request({
        url: '/system/sysUserMenuShortcut/' + data,
        method: 'delete',
    })
}
// -----------------------------代办中心-------------------------------------------

// 获取代办数量
export function agentCount(data) {
    return request({
        url: '/process/getAgencyCenterNumber',
        method: 'get',
        params:data
    })
}
// 获取代办详情
export function agentDetail(data) {
    return request({
        url: '/process/getAgencyCenter',
        method: 'get',
        params:data
    })
}

// -----------------------------我发起的-------------------------------------------

// 获取发起
export function initiateList() {
    return request({
        url: '/process/getInitiate',
        method: 'get',
    })
}
// -----------------------------我的消息 ------------------------------------------

// 列表
export function noticeList() {
    return request({
        url: '/msg/notice/list',
        method: 'get',
    })
}
// 列表
export function listHomePage() {
    return request({
        url: '/msg/notice/listHomePage',
        method: 'get',
    })
}
// 修改已读状态
export function noticeLook(id) {
    return request({
        url: '/msg/notice/' + id + '/view',
        method: 'put',
    })
}

// 通知公告
// 工作台我的消息查看更多 TYPE类型
export function noticeTypeList() {
  return request({
    url: `/msg/notice/getEnum`,
    method: 'get'
  });
}
// -----------------------------公文 ------------------------------------------

// 列表
export function documentList(data) {
    return request({
        url: '/document/documentUserReceive/searchDocumentUserList',
        method: 'post',
        data
    })
}
// 列表
export function documentHomePageList(data) {
    return request({
        url: '/document/documentUserReceive/searchDocumentUserListHomePage',
        method: 'post',
        data
    })
}
// -----------------------------分布图 ------------------------------------------

// 分布图数据
export function employeeBase() {
    return request({
        url: '/system/employeeBase/desk',
        method: 'get'
    })
}

// -----------------------------当日事项 ------------------------------------------

// 当日事项
export function dayEvents(data) {
    return request({
        url: '/process/getTheMatter/?category=' + data,
        method: 'get'
    })
}

// 当日事项 -- 二期
export function dayEventsList() {
    return request({
        url: '/system/sysUserRemind/eventsDayList',
        method: 'get'
    })
}

// 提前提醒
export function advanceRemind() {
    return request({
        url: '/system/sysUserRemind/list',
        method: 'post'
    })
}
// 新增提前提醒
export function addRemind(data) {
    return request({
        url: '/system/sysUserRemind',
        method: 'post',
        data
    })
}


// -----------------------------  申请中心 工作台 ------------------------------------------

// 申请中心 工作台
export function applyList(data) {
    return request({
        url: '/system/menu/applyList',
        method: 'post',
        data
    })
}

// 申请中心 工作台
export function remindList(data) {
    return request({
        url: '/system/sysUserRemind/remindList',
        method: 'post',
        data
    })
}