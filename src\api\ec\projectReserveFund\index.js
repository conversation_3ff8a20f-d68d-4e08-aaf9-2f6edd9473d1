import request from '@/utils/request'


// 获取项目备用金申请详细信息
export function projectImprest(id) {
	return request({
		url: `/fee/projectImprest/${id}`,
		method: 'get'
	});
}
// 修改项目备用金申请
export function putProjectImprest(data) {
	return request({
		url: '/fee/projectImprest',
		method: 'put',
    data:data
	});
}
// 新增项目备用金申请
export function postProjectImprest(data) {
	return request({
		url: '/fee/projectImprest',
		method: 'post',
    data:data
	});
}
// 根据当前登陆人userId获取银行信息
export function getBankInfo(id) {
  return request({
    url: `/system/bankCard/getBankInfoByUserId/${id}`,
    method: 'get',
  })
}

// 获取当前项目备用金余额：
export function lockerAmountsByProjectId(projectId) {
	return request({
		url: `/fee/projectImprest/lockerAmountsByProjectId/${projectId}`,
		method: 'get'
	});
}

// 获取预览
export function getPreviewData(id) {
	return request({
		url: `/fee/projectImprest/preview/projectImprest/`+id,
		method: 'get'
	});
}

export function getPreviewData2Export(id) {
  return request({
    url: `/fee/projectImprest/preview/projectImprest/export/`+id,
    method: 'get'
  });
}

