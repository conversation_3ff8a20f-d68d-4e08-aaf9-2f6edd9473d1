import request from '@/utils/request';

// 获取全员日报列表
export function monthlyAttendanceStatistics(query){
  return request({
    url: `/data/dailyStatistical/monthlyAttendanceStatistics`,
    method: 'POST',
    data: query
  })
}


// 获取日志统计
export function dailyStatistical(query){
  return request({
    url: `/data/dailyStatistical/dailyStatistics`,
    method: 'POST',
    data: query
  })
}
export function exportMonthlyAttendanceStatistics(query){
  return request({
    url: `/data/dailyStatistical/exportMonthlyAttendanceStatistics`,
    method: 'POST',
    data: query,
    responseType: 'blob',
  })
}

// 导出日志统计
export function exportDailyStatistics(query){
  return request({
    url: `/data/dailyStatistical/exportDailyStatistics`,
    method: 'POST',
    data: query,
    responseType: 'blob',
  })
}
export function computerRental(query){
  return request({
    url: `/data/computerRental/list`,
    method: 'POST',
    data: query
  })
}
// 月度采购费用统计
export function purchaseInfo(query){
  return request({
    url: `/data/purchaseInfo/list`,
    method: 'POST',
    data: query
  })
}
// 列表标题列
export function getTableTitle(){
  return request({
    url: `/data/purchaseInfo/title`,
    method: 'get',
  })
}

// 查询项目月度考勤统计
export function getWorkingHours(query){
  return request({
    url: `/project/monthlyAttendanceStatistics/list`,
    method: 'POST',
    data: query
  })
}

// 删除项目月度考勤统计
export function delWorkingHours(id){
  return request({
    url: `/project/monthlyAttendanceStatistics/${id}`,
    method: 'DELETE',
  })
}
