import request from '@/utils/request'

// 分页查询考勤组列表
export function listGroup(query) {
  return request({
    url: '/attendance/clockInGroup/list',
    method: 'get',
    params: query
  })
}

// 提交考勤组
export function addGroup(data) {
  return request({
    url: '/attendance/clockInGroup',
    method: 'post',
    data: data
  })
}

// 修改考勤组
export function updateGroup(data) {
  return request({
    url: '/attendance/clockInGroup',
    method: 'put',
    data: data
  })
}

// 删除考勤组
export function delGroup(id) {
  return request({
    url: '/attendance/clockInGroup/' + id,
    method: 'delete'
  })
}

// 查询考勤组详细
export function getGroup(id) {
  return request({
    url: '/attendance/clockInGroup/' + id,
    method: 'get'
  })
}

// 查询考勤组详细
export function listRecord(query) {
  return request({
    url: '/attendance/clockInRecord/list',
    method: 'get',
    params: query
  })
}

// 查询考勤组详细
export function downloadList(data) {
  return request({
    url: '/attendance/clockInRecord/exportList',
    method: 'post',
    responseType: 'blob',
    data:data
  })
}



