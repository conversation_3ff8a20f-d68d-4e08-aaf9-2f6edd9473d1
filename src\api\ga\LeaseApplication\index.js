import request from '@/utils/request'

// 电脑租赁 申请查询列表
export function computerRentalDetail(data) {
  return request({
    url: `/administration/computerRentalDetail/list`,
    method: 'post',
    data: data
  });
}

// 电脑租赁 导出查询列表
export function exportComputerRentalDetail(query) {
  return request({
    url: `/computerRentalDetail/exportComputerRentalDetail`,
    method: 'get',
    params: query,
    responseType: 'blob',
  });
}
  // 电脑租赁 查询是否导入过数据
export function checkComputerRentalImport() {
  return request({
    url: `/administration/computerRentalInfo/checkComputerRentalImport`,
    method: 'get'
  });
}
