import request from '@/utils/request'

// 投标类型数据
export function typeOptionsData() {
	return request({
		url: '/project/common/getProjectContractTypeEnum',
		method: 'get'
	});
}

// 投标状态数据
export function statusOptionsData() {
	return request({
		url: '/project/common/getBiddingStateEnum',
		method: 'get'
	});
}

// 投标列表数据
export function listTender(data) {
	return request({
	    url: '/project/biddingDocument/list',
	    method: 'post',
	    data: data
	})
}

//招标人数据
export function customerIdData() {
	return request({
		url: '/project/common/getCustomerInfoList',
		method: 'get'
	});
}
//投标人数据
export function companyIdData() {
	return request({
		url: '/project/common/getRegList',
		method: 'get'
	});
}

//查询正式项目列表
export function formalProjectList() {
	return request({
		url: '/project/formalProject/list',
		method: 'get'
	});
}

//查询项目列表
export function projectInfoList() {
	return request({
		url: '/project/projectInfo/list',
		method: 'post',
		data:{}
	});
}

//获取投标信息详细信息
export function biddingInfo(id) {
	return request({
		url: '/project/biddingDocument/' + id,
		method: 'get'
	});
}

//获取招标文件获取方式
export function getBiddingFileEnumData() {
	return request({
		url: '/project/common/getBiddingFileEnum',
		method: 'get'
	});
}

//获取甲方招标方式
export function getBiddingModeEnumData() {
	return request({
		url: '/project/common/getBiddingModeEnum',
		method: 'get'
	});
}

//查询员工
export function listUsersData(data) {
	return request({
		url: '/system/workInfo/listUsers',
		method: 'post',
    data:data
	});
}

//获取标书是否递交完成
export function getBiddingFinishedEnumData() {
	return request({
		url: '/project/common/getBiddingFinishedEnum',
		method: 'get'
	});
}

//新增投标信息
export function biddingInfoSubmit(data) {
	return request({
		url: '/project/biddingDocument',
		method: 'post',
		data: data
	});
}

//修改投标信息
export function putBiddingInfoSubmit(data) {
	return request({
		url: '/project/biddingDocument',
		method: 'put',
		data: data
	});
}

//新增中标通知信息
export function postNoticeAward(data) {
	return request({
		url: '/project/noticeAward',
		method: 'post',
		data: data
	});
}

//修改中标通知信息
export function putNoticeAward(data) {
	return request({
		url: '/project/noticeAward',
		method: 'put',
		data: data
	});
}

//获取中标+投标详细信息
export function noticeAwardInfo(id) {
	return request({
		url: '/project/noticeAward/' + id,
		method: 'get'
	});
}
export function abandon(data) {
	return request({
	    url: '/project/biddingDocument/updateStatus',
	    method: 'post',
	    data: data
	})
}

export function getNoticeInfo(data) {
	return request({
	    url: '/project/noticeAward/info',
	    method: 'get',
	    params: data
	})
}


