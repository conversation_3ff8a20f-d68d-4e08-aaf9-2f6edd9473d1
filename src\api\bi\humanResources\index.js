import request from "@/utils/request";

// 人资看板
export function humanResourcesBoard(query) {
  return request({
    url: `/data/humanResourcesBoard/all`,
    method: "post",
    params: query,
  });
}

// 人资看板
export function ratioAll(query) {
  return request({
    url: `/data//humanResourcesBoard/ratioAll`,
    method: "post",
    params: query,
  });
}

//新增，离职，空载列表
export function getUserList(query) {
  return request({
    url: `/data/humanResourcesBoard/getUserList`,
    method: "get",
    params: query,
  });
}

//当月各部门人员
export function getOrgChart(query) {
  return request({
    url: `/data/humanResourcesBoard/getOrgChart`,
    method: "get",
    params: query,
  });
}

//各部门空载率趋势
export function getOrgUserNullChart(query) {
  return request({
    url: `/data/humanResourcesBoard/getOrgUserNullChart`,
    method: "get",
    params: query,
  });
}

//各部门离职率
export function getOrgUserLeaveChart(query) {
  return request({
    url: `/data/humanResourcesBoard/getOrgUserLeaveChart`,
    method: "get",
    params: query,
  });
}
