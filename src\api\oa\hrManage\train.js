import request from '@/utils/request'

// 查询培训列表
export function listTrain(data) {
    return request({
        url: '/hr/hrCultivate/list',
        method: 'post',
        data
    })
}

// 查询培训列表 --
export function summaryListTrain(data) {
    return request({
        url: '/hr/hrCultivate/summaryList',
        method: 'post',
        data
    })
}

// 新增培训
export function addTrain(data) {
    return request({
        url: '/hr/hrCultivate',
        method: 'post',
        data: data
    })
}
// 修改培训
export function updateTrain(data) {
    return request({
        url: '/hr/hrCultivate',
        method: 'put',
        data: data
    })
}

// 删除培训
export function delTrain(id) {
    return request({
        url: '/hr/hrCultivate/' + id,
        method: 'delete'
    })
}
// 删除培训
export function delTrainInfo(id) {
    return request({
        url: '/hr/hrCultivateInfo/' + id,
        method: 'delete'
    })
}
// 培训详情
export function detailTrain(id) {
    return request({
        url: '/hr/hrCultivate/' + id,
        method: 'get'
    })
}

//文件删除
export function removeFile(id){
	return request({
        url: `/file/file/removeById/${id}`,
        method: 'delete',
	})
}