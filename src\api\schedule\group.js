import request from '@/utils/request'

// 进行查询管理分组列表
export function getList(data) {
    return request({
        url: `/system/groupInfo/list`,
        method: 'post',
        data
    })
}

// 新增管理分组
export function addGroup(data) {
    return request({
        url: `/system/groupInfo`,
        method: 'post',
        data
    })
}

// 修改管理分组
export function updataGroup(data) {
    return request({
        url: `/system/groupInfo`,
        method: 'put',
        data
    })
}

// 删除管理分组
export function deleteGroup(data) {
    return request({
        url: `/system/groupInfo/`+ data,
        method: 'delete',
    })
}


// 进行查询管理分组列表
export function getInfo(id) {
    return request({
        url: `/system/notice/${id}`,
        method: 'get',
    })
}