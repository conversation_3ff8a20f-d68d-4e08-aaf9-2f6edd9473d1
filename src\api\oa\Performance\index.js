import request from '@/utils/request'

// 进行查询月基本绩效信息列表
export function getList(data) {
	return request({
		url: `/hr/performanceMonthlyBasics/list`,
		method: 'post',
    data
	});
}

// 获取绩效类型下拉
export function getPerformanceType() {
	return request({
		url: `/hr/performanceMonthlyBasics/getPerformanceTypeEnumMap`,
		method: 'get',
	});
}


// 进行查询月基本绩效信息列表（月份）
export function getMonthList(data) {
	return request({
		url: `/hr/performanceMonthlyBasics/monthList`,
		method: 'post',
    data
	});
}

// 获取系统时间
export function getLocalTime() {
	return request({
		url: `/hr/performanceMonthlyBasicsSummary/getTime`,
		method: 'get',
	});
}

// 校验是否可提交月度汇总
export function isBasicsCount(data) {
	return request({
		url: `/hr/performanceMonthlyBasics/monthlyBasicsCount`,
		method: 'post',
    data
	});
}