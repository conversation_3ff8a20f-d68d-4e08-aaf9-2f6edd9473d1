import request from '@/utils/request'

// 获取供应商列表
export function getSupplier(data) {
  return request({
    url: '/system/supplierInfo/list',
    method: 'get',
    params:data
  });
}
// 搜索客户列表
export function searchCustomerInfo (data) {
  return request({
    url: "/system/customerInfo/list",
    method: "post",
    data: data,
  })
}

// 提交供应商
export function postSupplier(data) {
  return request({
    url: '/system/supplierInfo',
    method: 'POST',
    data:data
  });
}
// 修改供应商
export function putSupplier(data) {
  return request({
    url: '/system/supplierInfo',
    method: 'PUT',
    data:data
  });
}
// 获取供应商详情
export function getSupplierDetail(id) {
  return request({
    url: `/system/supplierInfo/info-dto/${id}`,
    method: 'GET',
  });
}
// 删除供应商详情
export function deleteSupplierDetail(id) {
  return request({
    url: `/system/supplierInfo/${id}`,
    method: 'DELETE',
  });
}
