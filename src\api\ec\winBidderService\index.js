import request from "@/utils/request";

// 获取已中标的投标编码  （新）20220817
export function winBidderOptionsData(data) {
  return request({
    url: "/project/biddingDocument/listAndNoticeCode",
    method: "post",
    data: data,
  });
}

//已中标详细数据
export function winbidderDetail(id) {
  return request({
    url: "/project/biddingDocument/winningBiddingDocument/" + id,
    method: "post",
  });
}

// 新增
export function postWinBidderService(data) {
  return request({
    url: "/fee/bidService",
    method: "post",
    data: data,
  });
}

// 编辑
export function putWinBidderService(data) {
  return request({
    url: "/fee/bidService",
    method: "put",
    data: data,
  });
}

// 中标服务费详细信息
export function WinBidderServiceDetail(id) {
  return request({
    url: "/fee/bidService/" + id,
    method: "get",
  });
}

// 预览单据
export function viewBidService(id) {
  return request({
    url: "/fee/bidService/preview/bidService/" + id,
    method: "get",
  });
}

// 导出单据
export function viewBidServiceExport(id) {
  return request({
    url: "/fee/bidService/preview/bidService/export/" + id,
    method: "get",
  });
}

