import request from '@/utils/request'

// 查询员工
export function getDirectManager(data) {
  return request({
    url: '/system/workInfo/listUsers',
    method: 'post',
    data:data
  })
}

// 新增修改保存接口
export function postOtherPay(query) {
  return request({
    url: '/hr/userMonthlyCount/batch',
    method: 'post',
    data: query
  })
}

// 获取当前期间
export function getYearMonth() {
  return request({
    url: '/hr/periodManagement/yearmonth',
    method: 'get',
  })
}

// 获取当前期间数据
export function getYearMonthList(pmId) {
  return request({
    url: `/hr/userMonthlyCount/pm-list/${pmId}`,
    method: 'get',
  })
}

// 删除其他薪资录入单
export function deleteOtherPay(id) {
  return request({
    url: `/hr/periodManagement/${id}`,
    method: 'delete',
  })
}

// 删除期间数据接口
export function deleteUserMonthlyCount(id) {
  return request({
    url: `/hr/userMonthlyCount/${id}`,
    method: 'delete',
  })
}
