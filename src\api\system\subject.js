import request from "@/utils/request";

export function reimbursementType(query) {
  return request({
    url: "/fee/reimbursementType/page",
    method: "post",
    data: query,
  });
}

export function saveOrUpdate(query) {
  return request({
    url: "/fee/reimbursementType/saveOrUpdate",
    method: "post",
    data: query,
  });
}

export function getReimbursementTypeHistory(id) {
  return request({
    url: `/fee/reimbursementType/getReimbursementTypeHistory/${id}`,
    method: "get",
  });
}
