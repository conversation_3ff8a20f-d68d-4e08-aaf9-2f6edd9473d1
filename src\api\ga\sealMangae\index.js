import request from '@/utils/request'

// 印章列表
export function sealList(data) {
    return request({
      url: `/hr/administrationSignet/list`,
      method: 'post',
      data: data
    });
}

// 新增印章
export function addSeal(data) {
    return request({
        url: '/hr/administrationSignet',
        method: 'post',
        data: data
    })
}

// 修改印章
export function updateSeal(data) {
    return request({
        url: '/hr/administrationSignet',
        method: 'put',
        data: data
    })
}

// 删除印章
export function delSeal(id) {
    return request({
        url: '/hr/administrationSignet/' + id,
        method: 'delete'
    })
}

// 查询单个印章信息
export function querySeal(id) {
    return request({
        url: '/hr/administrationSignet/' + id,
        method: 'get'
    })
}

// 查询顶级单位列表
export function unitList() {
    return request({
        url: '/system/organizational/getTopParentList',
        method: 'get'
    })
}

// 根据单位查询部门列表
export function postList(data) {
    return request({
        url: '/system/organizational/sublevelInfo',
        method: 'post',
        data
    })
}

// 用印申请
export function applySeal(data) {
    return request({
        url: '/hr/administrationSignetApply',
        method: 'post',
        data
    })
}

// 用印列表
export function applyList(data) {
    return request({
        url: '/hr/administrationSignetApply/list',
        method: 'post',
        data
    })
}
// 用印详情
export function applyDetail(data) {
    return request({
        url: '/hr/administrationSignetApply/' + data,
        method: 'get',
    })
}


