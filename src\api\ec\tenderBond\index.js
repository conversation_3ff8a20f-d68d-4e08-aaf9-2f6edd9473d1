import request from '@/utils/request'


// 投标列表数据 （新）20220817
export function listTender(data) {
	return request({
	    url: '/project/biddingDocument/list',
	    method: 'post',
	    data: data
	})
}

// 新增
export function postTenderBond(data) {
	return request({
		url: '/fee/bidBail',
		method: 'post',
		data: data
	})
}

// 编辑
export function putTenderBond(data) {
	return request({
		url: '/fee/bidBail',
		method: 'put',
		data: data
	})
}

// 投标保证金详细信息
export function bidBailDetail(id) {
	return request({
		url: '/fee/bidBail/' + id,
		method: 'get'
	})
}

// 预览单据
export function viewBidBail(id) {
  return request({
    url: '/fee/bidBail/preview/bidBail/' + id,
    method: 'get'
  })
}

// 导出单据
export function viewBidBailExport(id) {
  return request({
    url: '/fee/bidBail/preview/bidBail/export/' + id,
    method: 'get'
  })
}
