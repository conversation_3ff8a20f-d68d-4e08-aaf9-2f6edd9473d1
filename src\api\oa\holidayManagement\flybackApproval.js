import request from '@/utils/request'

// 获取flyback员工的信息
export function getFlyApplyUserInfo(id) {
  return request({
    url: `/hr/flyBack/getFlyApplyUserInfo/${id}`,
    method: 'get'
  })
}
// 新增flyback申请
export function saveOrUpdateFlyApply(data) {
  return request({
    url: '/hr/flyBack/saveOrUpdateFlyApply',
    method: 'POST',
    data: data
  })
}
// 获取flyback申请详情
export function getFlyApplyDetail(id) {
  return request({
    url: `/hr/flyBack/getFlyApplyDetail/${id}`,
    method: 'GET',
  })
}
