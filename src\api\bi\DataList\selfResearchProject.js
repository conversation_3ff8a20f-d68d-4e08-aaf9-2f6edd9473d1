import request from "@/utils/request";
// 自研项目看板
//自研项目看板统计面板
export function getInnerDetail(localDate) {
  return request({
    url: `/data/projectOverview/innerDetail?localDate=${localDate}`,
    method: "get",
  });
}
export function getInnerProjectInfo(id,query) {
  return request({
    url: `/project/projectInfo/getInnerProjectInfo/${id}`,
    method: "get",
    params:query
  });
}
// 自研项目看板穿透列表
export function getBySelf(url,data,method) {
  if(method === 'post'){
    return request({
      url: url,
      method: "post",
      data: data,
    });
  }else{
    return request({
      url: url,
      method: "get",
      params: data,
    });
  }
}