import request from '@/utils/request'

// 绩效方案列表
export function schemeList(data) {
	return request({
	    url: '/hr/performanceProgramme/list',
	    method: 'post',
	    data: data
	})
}
//绩效方案删除
export function delScheme(id) {
	return request({
		url: '/hr/performanceProgramme/' + id,
		method: 'delete'
	})
}
//绩效方案删除绩效指标关联
export function deleteProgrammeIndicators(data) {
	return request({
		url: '/hr/performanceProgramme/deleteProgrammeIndicators' ,
		method: 'delete',
		data:data
	})
}

// 获取绩效方案详情
export function getScheme(id) {
	return request({
		url: '/hr/performanceProgramme/' + id,
		method: 'get',
	})
}
// 新增绩效方案
export function saveScheme(data) {
	return request({
	    url: '/hr/performanceProgramme',
	    method: 'post',
	    data: data
	})
}
// 修改绩效方案
export function editScheme(data) {
	return request({
		url: '/hr/performanceProgramme',
		method: 'put',
		data: data
	})
}
// 绩效方案状态枚举
export function schemeStatusData() {
	return request({
		url: '/hr/performanceProgramme/getPerformanceProgrammeStatusEnum',
		method: 'get'
	});
}
// 绩效方案考评对象枚举
export function schemeTargetData() {
	return request({
		url: '/hr/performanceProgramme/getPerformanceProgrammeTargetEnum',
		method: 'get'
	});
}
// 绩效方案考评类型枚举
export function schemeTypeData() {
	return request({
		url: '/hr/performanceProgramme/getPerformanceProgrammeTypeEnum',
		method: 'get'
	});
}

// 是否有分数
export function isHaveScore(id) {
  return request({
    url: '/hr/performanceUser/isHaveScore/'+id,
    method: 'get'
  });
}









