import request from '@/utils/request'
import { praseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(data) {
    return request({
        url: '/system/user/list',
        method: 'post',
        data: data
    })
}

// 查询用户列表
export function rosterUserList(data) {
  return request({
      url: '/system/user/rosterUserList',
      method: 'post',
      data: data
  })
}

// 查询用户列表
export function rosterUserGroupList(data) {
  return request({
      url: '/system/user/rosterUserGroupList',
      method: 'post',
      data: data
  })
}

// 查询用户列表--出库领用人只可选择非离职
export function formalList(data) {
    return request({
        url: '/system/user/formalList',
        method: 'post',
        data: data
    })
}

// 查询退休列表
export function retireBasesList(data) {
    return request({
        url: '/hr/roster/retireBasesList',
        method: 'post',
        data: data
    })
}

// 查询花名册列表
export function listRosterUser(data) {
  return request({
      url: '/hr/roster/basesList',
      method: 'post',
      data: data
  })
}

// 查询离职列表
export function departBasesList(data) {
  return request({
      url: '/hr/roster/departBasesList',
      method: 'post',
      data: data
  })
}

// 查询部门树数据
export function getTreeSelect() {
    return request({
        url: '/hr/roster/treeselect',
        method: 'get',
    })
}

//查询部门和部门人数
export function listDeptPeopleNumber(query) {
    return request({
        url: '/system/user/deptPeopleNumber',
        method: 'get',
        params: query
    })
}

// 下载在职花名册
export function downloadDeptRoster(query) {
    return request({
        url: '/hr/roster/downloadBasesRoster',
        method: 'post',
        data: query,
        responseType: 'blob',
    })
}

// 下载部门花名册模板
export function downloadTemplate(query) {
  return request({
      url: '/hr/roster/download/template',
      method: 'get',
      responseType: 'blob',
  })
}

// 下载退休花名册
export function downloadRetire(query) {
  return request({
      url: '/hr/roster/downloadRetireBasesRoster',
      method: 'post',
      data: query,
      responseType: 'blob',
  })
}

// 下载离职花名册
export function downloadBases(query) {
  return request({
      url: '/hr/roster/downloadDepartBasesRoster',
      method: 'post',
      data: query,
      responseType: 'blob',
  })
}

// 获取组织列表
export function getOrgList(query) {
  return request({
    url: "/system/organizational/list",
    method: "post",
    data: query,
  });
}

// 用户置顶
export function rosterTop(userId) {
  return request({
      url: '/system/user/top/' + userId,
      method: 'get'
  })
}

// 退休用户置顶
export function retireTop(userId) {
  return request({
      url: '/system/user/retireTop/' + userId,
      method: 'get'
  })
}
// 离职用户置顶
export function departTop(userId) {
  return request({
      url: '/system/user/topDepart/' + userId,
      method: 'get'
  })
}

// 用户取消置顶
export function rosterCancelTop(userId) {
  return request({
      url: '/system/user/cancelTop/' + userId,
      method: 'get'
  })
}

// 调整排序/roster/sort
export function sort(data) {
  return request({
      url: '/hr/roster/sort',
      method: 'post',
      data:data
  })
}

// 查询职级专级
export function rankList(data) {
  return request({
      url: '/system/rank/list',
      method: 'post',
      data:data
  })
}

// 查询用户详细
export function getUser(userId) {
    return request({
        url: '/system/user/' + praseStrEmpty(userId),
        method: 'get'
    })
}
// 查询用户详细
export function getRosterUser(userId) {
    return request({
        url: '/system/user/getUserById/' + userId,
        method: 'get'
    })
}

// 查询用户详细
export function getPersonalUser(userId) {
  return request({
    url: '/system/user/getUserPersonalById/' + userId,
    method: 'get'
  })
}

export function submitAuditUser(userData){
  return request({
    url: '/system/user/updatePerfectUser/',
    method: 'put',
    data: userData
  })
}

export function submitUserNew(userData){
  return request({
    url: '/system/user/updateUser',
    method: 'post',
    data: userData
  })
}

// 新增用户
export function addUser(data) {
    return request({
        url: '/system/user',
        method: 'post',
        data: data
    })
}

// 修改用户
export function updateUser(data) {
    return request({
        url: '/system/user',
        method: 'put',
        data: data
    })
}
// 修改用户-新 - 修改用户角色
export function updateEditUser(data) {
    return request({
        url: '/system/user/editUserRole',
        method: 'put',
        data: data
    })
}

// 删除用户
export function delUser(userId) {
    return request({
        url: '/system/user/' + userId,
        method: 'delete'
    })
}

// 用户登录密码重置
export function resetUserPwd(id, password) {
    const data = {
        id,
        password
    }
    return request({
        url: '/system/user/resetPwd',
        method: 'put',
        data: data
    })
}

// 用户查薪密码重置
export function resetUserSalaryPwd(id, password) {
  const data = {
      id,
      salaryPassd:password
  }
  return request({
      url: '/system/user/resetSalaryPwd',
      method: 'put',
      data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
    const data = {
        id: userId,
        status
    }
    return request({
        url: '/system/user/changeStatus',
        method: 'put',
        data: data
    })
}

// 查询用户个人信息
export function getUserProfile() {
    return request({
        url: '/system/user/profile',
        method: 'get'
    })
}

// 修改用户个人信息
export function updateUserProfile(data) {
    return request({
        url: '/system/user/profile',
        method: 'put',
        data: data
    })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
    const data = {
        oldPassword,
        newPassword
    }
    return request({
        url: '/system/user/profile/updatePwd',
        method: 'put',
        params: data
    })
}

// 用户头像上传
export function uploadAvatar(data) {
    return request({
        url: '/system/user/profile/avatar',
        method: 'post',
        data: data
    })
}

// 修改密码
export function updatePwd(data) {
    return request({
        url: '/system/user/profile/updatePwd',
        method: 'put',
        data: data
    })
}

// 根据手机和身份证后六位获取token
export function retrievePwdToken(query) {
    return request({
        url: '/auth/retrievePwdToken',
        method: 'get',
        params: query
    })
}
// 重置密码
export function pwd(data) {
    return request({
        url: '/auth/pwd',
        method: 'PUT',
        data: data
    })
}

export function exportSocialList(query) {
    return request({
        url: '/hr/roster/uploadFile',
        method: 'post',
        data: query
    })
}

// 新增/修改专家
export function addOrUpdateExpertEmployeeBase(data) {
  return request({
    url: '/hr/roster/addOrUpdateExpertEmployeeBase',
    method: 'put',
    data: data
  })
}

// 新增/修改专家
export function downloadExpert(data) {
  return request({
    url: '/hr/roster/download',
    method: 'POST',
    responseType: 'blob',
    data
  })
}

/**
 * 新增/修改绩效系数
 * @param data
 * @param method put修改 post 新增
 * @returns {*}
 */
export function addOrUpdateCoefficient(data,method) {
  console.log(data)
  return request({
    url: '/hr/performanceCoefficient',
    method,
    data
  })
}

//绩效系数列表
export function coefficientList(data) {
  return request({
    url: '/hr/performanceCoefficient/list',
    method: 'post',
    data
  })
}
//绩效系数枚举
export function getCoefficientLevelEnum(data) {
  return request({
    url: '/hr/performanceCoefficient/getCoefficientLevelEnum',
    method: 'get',
    data
  })
}

//绩效系数可用枚举
export function getCoefficientLevelNotHaveEnum(data) {
  return request({
    url: '/hr/performanceCoefficient/getCoefficientLevelNotHaveEnum',
    method: 'get',
    data
  })
}


//删除绩效系数
export function delCoefficient(level) {
  return request({
    url: `/hr/performanceCoefficient/${level}`,
    method: 'delete'
  })
}

//指标列表
export function indicatorsList(data) {
  return request({
    url: `/hr/performanceIndicators/list`,
    method: 'post',
    data:data
  })
}

//删除绩效指标
export function delIndicators(id) {
  return request({
    url: `/hr/performanceIndicators/${id}`,
    method: 'delete'
  })
}



/**
 * 新增/修改指标
 * @param data
 * @param method put修改 post 新增
 * @returns {*}
 */
export function addOrUpdateIndicators(data,method) {
  return request({
    url: '/hr/performanceIndicators',
    method,
    data
  })
}

export function approveAgentList(data) {
  return request({
    url: '/hr/approveAgent/list',
    method:'post',
    data
  })
}


export function approveAgent(data,method,id) {
  let str = ''
  if(id){
    str = '/'+id
  }
  return request({
    url: '/hr/approveAgent'+str,
    method,
    data
  })
}

export function getApproveAgent(id) {
  return request({
    url: '/hr/approveAgent/'+id,
    method:'get'
  })
}

export function setApproveAgentOpen(id,open) {
  return request({
    url: `/hr/approveAgent/${id}/${open}/`,
    method:'put'
  })
}

export function getCoefficientDetail(level) {
  return request({
    url: `/hr/performanceCoefficient/${level}`,
    method:'get'
  })
}

export function approveAdminList(data) {
  return request({
    url: '/hr/approveAgent/list',
    method:'post',
    data
  })
}

export function clearUserCache() {
  return request({
    url: `/system/user/initCache`,
    method:'get'
  })
}

export function perfect(data) {
  return request({
    url: `/system/user/perfect`,
    method:'post',
    data
  })
}


//退休状态
export function getStatus() {
  return request({
    url: `/system/dict/data/type/sys_staff_status`,
    method:'get'
  })
}





