import request from '@/utils/request'

// 查询人员基本信息列表
export function employeeRegularList(query) {
  return request({
    url: '/hr/employment/list',
    method: 'post',
    data: query
  })
}

// 查询转正人员信息
export function employeeRegular(id) {
  return request({
    url: `/hr/employment/getUserInfo/${id}`,
    method: 'get'
  })
}
// 回显转正人员信息
export function getEmployeeRegularDetail(id) {
  return request({
    url: `/hr/employment/getUserByPersonChangeId/${id}`,
    method: 'get'
  })
}
// 提交转正人员信息
export function putEmployeeRegular(query) {
  return request({
    url: `/hr/employment/regular`,
    method: 'put',
    data: query
  })
}

// 提交延期转正人员信息
export function putEmployeeDeferredRegular(query) {
  return request({
    url: `/hr/employment/regularDelay`,
    method: 'put',
    data: query
  })
}
