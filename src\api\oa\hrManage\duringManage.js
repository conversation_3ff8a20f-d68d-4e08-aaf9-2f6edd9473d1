import request from '@/utils/request'

// 期间管理列表
export function getDuringList(query) {
  return request({
    url: 'hr/periodManagement/list',
    method: 'get',
    params: query
  })
}

// 修改期间管理状态获取最新月
export function getNewDate(status) {
  return request({
    url: `hr/periodManagement/yearmonth/${status}`,
    method: 'get'
  })
}
// 修改期间管理状态
export function reviseDuringStatus(id) {
  return request({
    url: `hr/periodManagement/${id}`,
    method: 'put',
  })
}

