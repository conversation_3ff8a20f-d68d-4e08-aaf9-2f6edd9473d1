import request from '@/utils/request'

// 查询综合列表
export function listCommunist(data) {
    return request({
        url: '/hr/hrCommunistAnnual/list',
        method: 'post',
        data
    })
}

// 查询历年列表
export function listHistory(data) {
    return request({
        url: '/hr/hrCommunistAnnual/annualList',
        method: 'post',
        data
    })
}

// 查询党员列表
export function listPartyMember(data) {
    return request({
        url: '/system/workInfo/listPartyMemberUsers',
        method: 'post',
        data
    })
}

// 查询详情
export function listRecord(data) {
    return request({
        url: '/hr/hrCommunistAnnual/' + data,
        method: 'get'
    })
}

// 新增记录
export function addCommunist(data) {
    return request({
        url: '/hr/hrCommunistAnnual',
        method: 'post',
        data: data
    })
}
// 修改记录
export function updateCommunist(data) {
    return request({
        url: '/hr/hrCommunistAnnual',
        method: 'put',
        data: data
    })
}

// 删除记录
export function delCommunist(id) {
    return request({
        url: '/hr/hrCommunistAnnual/' + id,
        method: 'delete'
    })
}
// 导出历年数据
export function downHistoryData(query) {
    return request({
        url: '/hr/hrCommunistAnnual/downloadAnnualList',
        method: 'post',
        data: query,
        responseType: 'blob',
    })
}
// 导出综合查询数据
export function downData(query) {
    return request({
        url: '/hr/hrCommunistAnnual/downloadList',
        method: 'post',
        data: query,
        responseType: 'blob',
    })
}
