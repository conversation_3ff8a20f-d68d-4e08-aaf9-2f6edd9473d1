import request from '@/utils/request'

// 进行查询机关代字下拉
export function getOrganList(data) {
  return request({
    url: '/document/organCode/list',
    method: 'post',
    data
  })
}

// 进行新增公文期号预选
export function dmOrganReservation(data) {
  return request({
    url: '/document/dmOrganReservation',
    method: 'post',
    data
  })
}

// 公文期号预选列表
export function getDmOrganList(data) {
  return request({
    url: '/document/dmOrganReservation/list',
    method: 'post',
    data
  })
}

// 进行删除公文期号预选信息
export function delDmOrgan(id) {
  return request({
    url: `/document/dmOrganReservation/${id}`,
    method: 'delete'
  })
}
