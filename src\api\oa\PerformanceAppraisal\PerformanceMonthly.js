import request from '@/utils/request'

// 人员月度绩效考评方案列表
export function monthlySchemeList(data) {
	return request({
	    url: '/hr/performanceUser/list',
	    method: 'post',
	    data: data
	})
}
// 人员月度绩效考评方案列表(人资)
export function monthlySchemeHrList(data) {
	return request({
	    url: '/hr/performanceUser/hrList',
	    method: 'post',
	    data: data
	})
}
// 调整人员月度绩效考评方案
export function adjustMonthlyScheme(data) {
	return request({
		url: '/hr/performanceUser',
		method: 'put',
		data: data
	})
}
// 定时任务生成人员月度绩效方案
export function produceMonthlySchemeList(data) {
	return request({
	    url: '/hr/performanceUser/addPerformanceUser',
	    method: 'get',
	    params: data
	})
}







