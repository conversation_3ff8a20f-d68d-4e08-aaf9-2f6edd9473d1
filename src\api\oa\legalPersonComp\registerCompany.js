import request from '@/utils/request'

// 查询注册公司信息列表
export function listRegisterCompany(data) {
  return request({
    url: '/system/registerCompany/listPaySubject',
    method: 'post',
    data
  })
}

// 查询注册公司信息详细
export function getRegisterCompany(id) {
  return request({
    url: '/system/registerCompany/' + id,
    method: 'get'
  })
}

// 新增注册公司信息
export function addRegisterCompany(data) {
  return request({
    url: '/system/registerCompany',
    method: 'post',
    data: data
  })
}

// 修改注册公司信息
export function updateRegisterCompany(data) {
  return request({
    url: '/system/registerCompany',
    method: 'put',
    data: data
  })
}

// 删除注册公司信息
export function delRegisterCompany(id) {
  return request({
    url: '/system/registerCompany/' + id,
    method: 'delete'
  })
}
