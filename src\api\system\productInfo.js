import request from '@/utils/request'

// 查询合同业务类型
export function productInfoList(data) {
    return request({
      url: '/zzy-system/projectProductInfo/list',
      method: 'post',
      data
    })
  }

  // 新增
export function addProductInfo(data) {
    return request({
      url: `/zzy-system/projectProductInfo`,
      method: 'post',
      data
    })
  }

  // 修改
export function update(data) {
    return request({
      url: `/zzy-system/projectProductInfo`,
      method: 'put',
      data
    })
  }

  // 删除
export function dalete(id) {
    return request({
      url: `/zzy-system/projectProductInfo/${id}`,
      method: 'delete',
    })
  }

