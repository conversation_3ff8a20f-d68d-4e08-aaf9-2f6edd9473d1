import request from '@/utils/request'

// 维修列表
export function meedList(data) {
    return request({
        url: `/administration/maMaintenanceApplyDetail/list`,
        method: 'post',
        data: data
    });
}
// 维修列表 - 已完成
export function meedEndList(data) {
    return request({
        url: `/administration/maMaintenanceApplyDetail/endList`,
        method: 'post',
        data: data
    });
}

// 维修申请
export function meedApply(data) {
    return request({
        url: `/administration/maMaintenanceApply`,
        method: 'post',
        data: data
    });
}

// 申请详情
export function meedDetail(data) {
    return request({
        url: `/administration/maMaintenanceApply/` + data,
        method: 'get',
    });
}

// 维修详情
export function meedListDetail(data) {
    return request({
        url: `/administration/maMaintenance/` + data,
        method: 'get',
    });
}

// 维修通知
export function meedNotice(data) {
    return request({
        url: `/administration/maMaintenance/`,
        method: 'post',
        data
    });
}
