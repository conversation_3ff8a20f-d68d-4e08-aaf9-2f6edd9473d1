import request from '@/utils/request'

// 获取备用金剩余金额
export function getLockerAmount(projectId) {
  return request({
    url: `/fee/projectImprest/lockerAmountsByProjectId/${projectId}`,
    method: 'get',
  })
}

// 提交保存修改
export function postNonProjectReimbursement(data) {
  return request({
    url: '/fee/nonProjectReimbursement/saveOrUpdate',
    method: 'post',
    data: data
  })
}
// 获取详情
export function getNonProjectReimbursement(id) {
  return request({
    url: `/fee/nonProjectReimbursement/process/detail/${id}`,
    method: 'get',
  })
}
// 根据当前登陆人userId获取银行信息
export function getBankInfo(id) {
  return request({
    url: `/system/bankCard/getBankInfoByUserId/${id}`,
    method: 'get',
  })
}
// 校验【差旅费-出差补助；差旅费-住宿费】规则
export function checkProjectReimbursement(data) {
  return request({
    url: `/fee/projectReimbursement/checkRules`,
    method: 'post',
    data: data
  })
}
