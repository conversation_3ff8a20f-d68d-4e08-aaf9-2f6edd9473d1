import request from '@/utils/request'

// 会议列表
export function meetingList(data) {
    return request({
        url: `/administration/administrationMeeting/list`,
        method: 'post',
        data: data
    });
}

// 新增会议
export function addMeeting(data) {
    return request({
        url: '/administration/administrationMeeting',
        method: 'post',
        data: data
    })
}

// 修改会议
export function updateMeeting(data) {
    return request({
        url: '/administration/administrationMeeting',
        method: 'put',
        data: data
    })
}

// 删除会议
export function delMeeting(id) {
    return request({
        url: '/administration/administrationMeeting/' + id,
        method: 'delete'
    })
}

// 查询顶级单位 会议室归属
export function meetingBelong(id) {
    return request({
        url: '/system/organizational/getTopParentList',
        method: 'get'
    })
}

//-------------------------- 会议申请 ----------------------------------

// 申请列表
export function applyList(data) {
    return request({
        url: `/administration/administrationMeetingApply/list`,
        method: 'post',
        data: data
    });
}
// 申请详情
export function applyDetail(data) {
    return request({
        url: `/administration/administrationMeetingApply/` + data,
        method: 'get',
    });
}

// -----------------------------会议安排----------------------------------

// 会议安排列表
export function scheduleList(data) {
    return request({
        url: `/administration/administrationMeetingArrange/manageList`,
        method: 'post',
        data: data
    });
}

// 会议安排 删除会议
export function scheduleDelete(data) {
    return request({
        url: `/administration/administrationMeetingArrange/cancel/`,
        method: 'post',
        data: data
    });
}


// -----------------------------会议工作台----------------------------------

// 会议工作台安排列表
export function stagingList(data) {
    return request({
        url: `/administration/administrationMeetingArrange/stagingList`,
        method: 'post',
        data: data
    });
}

// 会议工作台 表头参会信息
export function countList() {
    return request({
        url: `/administration/administrationMeetingArrange/meetingCount`,
        method: 'get'
    });
}

// 会议工作台 查看单个会议
export function meetingDetail(data) {
    return request({
        url: `/administration/administrationMeetingArrange/` + data,
        method: 'get'
    });
}

// 会议工作台 添加简会预约
export function addBrief(data) {
    return request({
        url: `/administration/administrationMeetingApply/simpleAdd`,
        method: 'post',
        data: data
    });
}


// 会议工作台
// 查询会议室列表-可预约
export function getList(data) {
    return request({
        url: `/administration/administrationMeeting/reservationList`,
        method: 'post',
        data: data
    });
}

// 新增普通会议
export function ordinaryAdd(data) {
    return request({
        url: `administration/administrationMeetingApply/ordinaryAdd`,
        method: 'post',
        data: data
    });
}


//-------------------------签到-----------------------------------------

// 签到/未签到列表
export function signList(data) {
    return request({
        url: `/administration/administrationMeetingSign/list`,
        method: 'post',
        data: data
    });
}

// 签到/不签 操作
export function signOperate(data) {
    return request({
        url: `/administration/administrationMeetingSign/sign`,
        method: 'post',
        data: data
    });
}

// 查看签到情况
export function signDetail(data) {
    return request({
        url: `/administration/administrationMeetingSign/getSign/` + data,
        method: 'get',
    });
}

// 查看二维码图片
export function codePicture(data) {
    return request({
        url: `/administration/administrationMeetingArrange/qrCode`,
        method: 'post',
        data:data
    });
}

// 导出数据
export function download(data) {
    return request({
        url: `/administration/administrationMeetingSign/downloadSignList`,
        method: 'post',
        data:data,
        responseType: 'blob',
    });
}