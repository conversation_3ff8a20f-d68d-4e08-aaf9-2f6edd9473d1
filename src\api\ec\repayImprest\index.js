import request from '@/utils/request'
// 提交保存修改
export function savePettyCash(data) {
  return request({
    url: '/fee/pettyCash/saveOrUpdateData',
    method: 'post',
    data: data
  })
}
// 获取详情
export function getPettyCashDetail(id) {
  return request({
    url: `/fee/pettyCash/detail/${id}`,
    method: 'get',
  })
}

// 获取当前项目备用金余额：
export function lockerAmountsByProjectId(projectId) {
	return request({
		url: `/fee/projectImprest/lockerAmountsByProjectId/${projectId}`,
		method: 'get'
	});
}

// 获取预览
export function getPreviewData(id) {
	return request({
		url: `/fee/pettyCash/preview/pettyCash/`+id,
		method: 'get'
	});
}

export function getPreviewDataExport(id) {
  return request({
    url: `/fee/pettyCash/preview/pettyCash/export/`+id,
    method: 'get'
  });
}
