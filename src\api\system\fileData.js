import request from '@/utils/request'
//资料库

// 查询资料库
export function getAllFileData(data){
  return request({
    url:'/system/fileData/list',
    method:'post',
    data
  })
}

// 查询我的资料库
export function getMyFileData(data){
  return request({
    url:'/system/fileData/myList',
    method:'post',
    data
  })
}
// 资料库详情
export function getFileDataDetail(id){
  return request({
    url:'/system/fileData/'+id,
    method:'get'
  })
}

// 资料库删除
export function removeFileData(id){
  return request({
    url:'/system/fileData/'+id,
    method:'delete'
  })
}

// 资料库修改
export function fileDataAddOrEdit(data,method){
  return request({
    url:'/system/fileData/',
    method,
    data
  })
}

// 全文检索
export function getFileList(data){
  return request({
    url:'/file/getFileList?content='+data.content,
    method:'post',
  })
}


