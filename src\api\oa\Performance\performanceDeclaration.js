import request from '@/utils/request'

// 重要会议或活动筹备申报
// 查询当前登录人的部门成员
export function getOrgList() {
	return request({
		url: `/system/workInfo/orgListUsers`,
		method: 'get'
	});
}


// 进行查询重要会议或活动筹备申报
export function getMeetingList(data) {
	return request({
		url: `/hr/performanceMoveAbout/getInfo`,
		method: 'post',
    data:data,
	});
}

// 进行提交/暂存重要会议或活动筹备申报
export function performanceMoveAbout(data) {
	return request({
	    url: '/hr/performanceMoveAbout',
	    method: 'post',
	    data: data
	})
}

// 加班申请
// 进行查询聘用人员加班申请
export function getOverTimeList(data) {
	return request({
		url: `/hr/performanceEngageWork/getInfo`,
		method: 'post',
    data:data,
	});
}

// 查询当前登录人的部门成员
export function getOrgUserList(id) {
	return request({
		url: `/system/workInfo/orgListUsers?userStatus=${id}`,
		method: 'get'
	});
}

// 查询当前登录人的部门成员
export function getOrgUserAll(data) {
	return request({
		url: `/system/workInfo/orgListUsers`,
		method: 'post',
		data
	});
}


// 进行提交/暂存聘用人员加班申请
export function performanceEngageWork(data) {
	return request({
		url: `/hr/performanceEngageWork`,
		method: 'post',
    data:data,
	});
}

// 重要文稿
// 进行查询重要文稿申报信息
export function getDraftList(data) {
	return request({
		url: `/hr/performanceManuscript/getInfo`,
		method: 'post',
    data:data,
	});
}

// 查询文稿类型下拉框接口
export function getDocTypeList() {
	return request({
		url: `/system/dict/data/type/sys_manuscript_type`,
		method: 'get'
	});
}

// 进行提交/暂存重要文稿申报信息
export function performanceManuscript(data) {
	return request({
		url: `/hr/performanceManuscript`,
		method: 'post',
    data:data,
	});
}

//月度基础性绩效申报
//进行查询月基本绩效信息
export function getBasicList(data) {
	return request({
		url: `/hr/performanceMonthlyBasics/getInfo`,
		method: 'post',
    data:data,
	});
}

//进行查询月基本绩效信息
export function performanceMonthlyBasics(data) {
	return request({
		url: `/hr/performanceMonthlyBasics`,
		method: 'post',
    data:data,
	});
}

// 值班申报
//进行查询值班申报信息
export function getDutyList(data) {
	return request({
		url: `/hr/performanceBeOnDuty/getInfo`,
		method: 'post',
    data:data,
	});
}

//进行提交/暂存值班申报
export function performanceBeOnDuty(data) {
	return request({
		url: `/hr/performanceBeOnDuty`,
		method: 'post',
    data:data,
	});
}