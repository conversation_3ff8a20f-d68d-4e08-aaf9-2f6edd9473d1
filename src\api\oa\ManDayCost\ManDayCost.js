import request from "@/utils/request";

// 查询人天成本牌价维护列表
export function humanResourceCostList(data) {
  return request({
    url: "/fee/humanResourceCost/list",
    method: "post",
    data: data,
  });
}

// 新增，修改人天成本牌价维护列表
export function humanResourceCost(method, data) {
  return request({
    url: "/fee/humanResourceCost",
    method: method,
    data: data,
  });
}

// 删除人天成本牌价维护列表
export function DeleteHumanResource(url) {
  return request({
    url: url,
    method: "delete",
  });
}

// 历史成本
export function historyList(data) {
  return request({
    url: "/fee/humanResourceCost/historyList",
    method: "post",
    data: data,
  });
}

// 批量审批日报列表
export function batchApprovalList(data) {
  return request({
    url: "/project/dailyRecord/batchApproval",
    method: "get",
    params: data,
  });
}
