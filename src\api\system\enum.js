import request from '@/utils/request'

export function getEducationEnumMap() {
  return request({
    url: '/hr/common/getEducationEnumMap',
    method: 'get',
  })
}
export function getMaritalEnumMap() {
  return request({
    url: '/hr/common/getMaritalEnumMap',
    method: 'get',
  })
}
export function getNativeTypeEnumMap() {
  return request({
    url: '/hr/common/getNativeTypeEnumMap',
    method: 'get',
  })
}
export function getPoliticalOutlookEnumMap() {
  return request({
    url: '/hr/common/getPoliticalOutlookEnumMap',
    method: 'get',
  })
}
export function getReasonsForResignationEnum() {
  return request({
    url: '/hr/common/getReasonsForResignationEnum',
    method: 'get',
  })
}
export function getSalaryCalculationMethodEnumMap() {
  return request({
    url: '/hr/common/getSalaryCalculationMethodEnumMap',
    method: 'get',
  })
}
export function getSocialModelEnumMap() {
  return request({
    url: '/hr/common/getSocialModelEnumMap',
    method: 'get',
  })
}
export function getTypeContractEnumMap() {
  return request({
    url: '/hr/common/getTypeContractEnumMap',
    method: 'get',
  })
} export function getUserEmployeeStatusEnumMap() {
  return request({
    url: '/hr/common/getUserEmployeeStatusEnumMap',
    method: 'get',
  })
}
export function getUserEmployeeTypeEnum() {
  return request({
    url: '/hr/common/getUserEmployeeTypeEnum',
    method: 'get',
  })
}
export function getUserTypeEnum() {
  return request({
    url: '/hr/common/getUserTypeEnum',
    method: 'get',
  })
}
export function getBusinessAdministrationEnumMap() {
  return request({
    url: '/hr/common/getBusinessAdministrationEnumMap',
    method: 'get',
  })
}

// 详情页页面查询
export function employeeInfo(id) {
  return request({
    url: `/hr/roster/${id}`,
    method: 'get',
  })
}
// 详情页页面查询---请假明细
export function vacationInfo(id) {
  return request({
    url: `/attendance/leaveRecord/getRecords/${id}`,
    method: 'get',
  })
}

//修改个人信息-基本信息
export function updateRosterUser(data) {
  return request({
    url: '/system/employeeBase/updateRosterBase',
    // url: '/system/user/updateRosterUser',
    method:'put',
    data
  })
}

//修改个人信息-任职信息
export function updateRosterUserWork(data) {
  return request({
    // url: '/system/user/updateRosterUserWork',
    url: '/system/workInfo/updateRosterUserWork',
    method:'put',
    data
  })
}

//修改个人信息-联系信息
export function updateRosterUserEmergency(data) {
  return request({
    // url: '/system/user/updateRosterUserEmergency',
    url: '/system/emergencyContact/updateRosterUserEmergency',
    method:'put',
    data
  })
}

//修改个人信息-家庭信息
export function updateRosterUserFamily(data) {
  return request({
    url: '/system/user/updateRosterUserFamily',
    method:'put',
    data
  })
}

//修改个人信息-证件信息
export function updateRosterUserDocument(data) {
  return request({
    url: '/system/user/updateRosterUserDocument',
    method:'put',
    data
  })
}

//修改个人信息-个人经历
export function updateRosterUserUndergo(data) {
  return request({
    url: '/system/user/updateRosterUserUndergo',
    method:'put',
    data
  })
}

//修改个人信息-教职信息
export function updateRosterUserTeaching(data) {
  return request({
    // url: '/system/user/updateRosterUserTeaching',
    url: '/system/sysTeachingPost/updateRosterUserTeaching',
    method:'put',
    data
  })
}

//修改个人信息-培训记录
export function updateRosterUserCultivate(data) {
  return request({
    url: '/hr/hrCultivate/updateRosterUserCultivate',
    method:'put',
    data
  })
}

//修改个人信息-档案认定记录
export function updateRosterUserFiles(data) {
  return request({
    url: '/hr/hrFilesPosit/updateRosterUserFiles',
    method:'put',
    data
  })
}

//再次更改个人信息-基本信息
export function changeRosterUser(data) {
  return request({
    url: '/system/user/changeRosterUser',
    method:'put',
    data
  })
}

//再次更改个人信息-联系信息
export function changeRosterUserEmergency(data) {
  return request({
    url: '/system/user/changeRosterUserEmergency',
    method:'put',
    data
  })
}

//再次更改个人信息-家庭信息
export function changeRosterUserFamily(data) {
  return request({
    url: '/system/user/changeRosterUserFamily',
    method:'put',
    data
  })
}

//再次更改个人信息-证件信息
export function changeRosterUserDocument(data) {
  return request({
    url: '/system/user/changeRosterUserDocument',
    method:'put',
    data
  })
}

//再次更改个人信息-个人经历
export function changeRosterUserUndergo(data) {
  return request({
    url: '/system/user/changeRosterUserUndergo',
    method:'put',
    data
  })
}

//再次更改个人信息-教职信息
export function changeRosterUserTeaching(data) {
  return request({
    url: '/system/user/changeRosterUserTeaching',
    method:'put',
    data
  })
}


export function addCultivate(data) {
  return request({
    url: '/hr/hrCultivate/',
    method:'post',
    data
  })
}

export function editCultivate(data) {
  return request({
    url: '/hr/hrCultivate/',
    method:'put',
    data
  })
}

export function addPosit(data) {
  return request({
    url: '/hr/hrFilesPosit/',
    method:'post',
    data
  })
}

export function editPosit(data) {
  return request({
    // url: '/hr/hrFilesPosit/',
    url: '/hr/hrFilesPosit/updateRosterUserFilesPosit',
    method:'put',
    data
  })
}

export function delPosit(id) {
  return request({
    url: '/hr/hrFilesPosit/'+id,
    method: 'delete',
  })
}

export function addEducationInfo(data) {
  return request({
    url: '/system/educationInfo/',
    method:'post',
    data
  })
}

export function editEducationInfo(data) {
  return request({
    // url: '/system/educationInfo/',
    url: '/system/educationInfo/updateRosterUserEducation',
    method:'put',
    data
  })
}

export function delEducationInfo(id) {
  return request({
    url: '/system/educationInfo/'+id,
    method: 'delete',
  })
}

export function addWorkUndergo(data) {
  return request({
    url: '/system/sysWorkUndergo/',
    method:'post',
    data
  })
}

export function editWorkUndergo(data) {
  return request({
    // url: '/system/sysWorkUndergo/',
    url: '/system/sysWorkUndergo/updateRosterUserWorkUndergo',
    method:'put',
    data
  })
}

export function delWorkUndergo(id) {
  return request({
    url: '/system/sysWorkUndergo/'+id,
    method: 'delete',
  })
}

export function addUserCredentials(data) {
  return request({
    url: '/system/sysUserCredentials/',
    method:'post',
    data
  })
}

export function editUserCredentials(data) {
  return request({
    // url: '/system/sysUserCredentials/',
    url: '/system/sysUserCredentials/updateRosterUserCredentials',
    method:'put',
    data
  })
}

export function delUserCredentials(id) {
  return request({
    url: '/system/sysUserCredentials/'+id,
    method: 'delete',
  })
}

export function addFamilyInfo(data) {
  return request({
    url: '/system/familyInfo/',
    method:'post',
    data
  })
}

export function editFamilyInfo(data) {
  return request({
    // url: '/system/familyInfo/',
    url: '/system/familyInfo/updateRosterUserFamily',
    method:'put',
    data
  })
}

export function delFamilyInfo(id) {
  return request({
    url: '/system/familyInfo/'+id,
    method: 'delete',
  })
}

export function addUserDocument(data) {
  return request({
    url: '/system/userDocument/',
    method:'post',
    data
  })
}

export function editUserDocument(data) {
  return request({
    // url: '/system/userDocument/',
    url: '/system/userDocument/updateRosterUserDocument',
    method:'put',
    data
  })
}

export function delUserDocument(id) {
  return request({
    url: '/system/userDocument/'+id,
    method: 'delete',
  })
}
//花名册保存头像
export function updateRosterUserInfo(data) {
  return request({
    url: '/system/user/updateRosterUserInfo',
    method:'put',
    data
  })
}