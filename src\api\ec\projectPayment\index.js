import request from '@/utils/request'

// 获取付款科目
export function getPaymentType() {
  return request({
    url: '/fee/common/getPayTypeEnum',
    method: 'get',
  })
}

// 新增项目付款
export function postProjectPayment(query) {
  return request({
    url: '/fee/projectPay',
    method: 'post',
    data: query
  })
}

// 修改项目付款
export function putProjectPayment(query) {
  return request({
    url: '/fee/projectPay',
    method: 'put',
    data: query
  })
}

// 获取项目付款详情
export function getProjectPaymentDetail(id) {
  return request({
    url: `/fee/projectPay/${id}`,
    method: 'get',
  })
}
