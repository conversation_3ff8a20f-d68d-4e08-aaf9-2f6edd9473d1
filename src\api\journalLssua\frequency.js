import request from '@/utils/request'

// 分页查询公文模板列表
export function listFrequency(query) {
  return request({
    url: '/manuscript/frequency/list',
    method: 'post',
    data: query
  })
}

// 新增公文模板
export function addFrequency(data) {
  return request({
    url: '/manuscript/frequency',
    method: 'post',
    data: data
  })
}

// 修改公文模板
export function editFrequency(data) {
  return request({
    url: '/manuscript/frequency',
    method: 'put',
    data: data
  })
}

// 删除公文模板
export function delFrequency(id) {
  return request({
    url: '/manuscript/frequency/' + id,
    method: 'delete'
  })
}

// 查询公文模板详细
export function getFrequency(id) {
  return request({
    url: '/manuscript/frequency/' + id,
    method: 'get'
  })
}


