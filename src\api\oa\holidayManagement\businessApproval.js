import request from '@/utils/request'

// 获取员工的信息
export function getEmployeeInformation(id) {
  return request({
    url: '/hr/employeeSalary/userSalary/?userId=' + id,
    method: 'get'
  })
}
// 新增出差申请
export function postBusinessTravel(data) {
  return request({
    url: '/hr/businessTravel/save',
    method: 'POST',
    data: data
  })
}
// 获取出差申请详情
export function getBusinessTravelDetail(id) {
  return request({
    url: `/hr/businessTravel/detail/${id}`,
    method: 'GET',
  })
}
