import request from '@/utils/request'

// 获取人员类型枚举
export function getStaffType() {
  return request({
    url: '/hr/common/getUserEmployeeTypeEnum',
    method: 'get',
  })
}

// 获取岗位类型/级别下拉
export function getGrade(data) {
  return request({
    url: '/system/sysPostType/list',
    method: 'post',
    data
  })
}

// 查询直属主管人员
export function getDirectManager(data) {
  return request({
    url: '/system/workInfo/listUsers',
    method: 'post',
    data: data
  })
}

// 根据uiserId查询个人详情
export function getEmployeeDetail(id) {
  return request({
    url: `/hr/induction/${id}`,
    method: 'get'
  })
}

// 提交人员基本信息列表
export function postEmployeeEntry(query) {
  return request({
    url: '/hr/induction/add',
    method: 'post',
    data: query
  })
}

// 修改人员基本信息列表
export function putEmployeeEntry(query) {
  return request({
    url: '/hr/induction/edit',
    method: 'put',
    data: query
  })
}

//上传证件照
export function upload(query) {
  return request({
    url: '/prod-api/file/upload',
    method: 'post',
    data: query
  })
}
// ------------------------新增人员功能列表----------------------- 

// 新增员工列表
export function getEmployeeList(data) {
  return request({
    url: `/hr/induction/list`,
    method: 'post',
    data:data
  })
}
// 新增员工详情
// export function getEmployeeDetail(data) {
//   return request({
//     url: `/system/induction/list`,
//     method: 'post',
//     data:data
//   })
// }