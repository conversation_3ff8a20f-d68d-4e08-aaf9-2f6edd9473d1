import request from '@/utils/request'

// 合同列表数据
export function getContractList(data) {
  return request({
    url: '/project/contractInfo/list',
    method: 'post',
    data: data
  });
}

// 获取甲方信息
export function getFirstParty() {
  return request({
    url: '/project/common/getCustomerInfoList',
    method: 'get',
  });
}

// 获取乙方信息
export function getSecondParty() {
  return request({
    url: '/project/common/getRegList',
    method: 'get',
  });
}

// 获取投标编码
// export function getTenderCode() {
//   return request({
//     url: '/project/biddingDocument/listAndNoticeCode',
//     method: 'post',
//   });
// }
// 获取投标编码(新) 2022-08-17修改
export function getTenderCode() {
  return request({
    url: '/project/biddingDocument/listAndNoticeCode',
    method: 'post',
  });
}

// 获取项目编码
export function getProjectCode() {
  return request({
    url: '/project/projectInfo/list',
    method: 'post',
    data: {}
  });
}

// 获取合同类型
export function getContractType() {
  return request({
    url: '/project/common/getContractTypeList',
    method: 'get',
  });
}

// 获取合同业务类型
export function getContractBusTypeList() {
  return request({
    url: '/project/contractType/list',
    method: 'post',
    data: {}
  });
}

// 提交保存接口
export function postSignContract(data) {
  return request({
    url: '/project/contractInfo/dto',
    method: 'post',
    data: data
  });
}

// 修改
export function putSignContract(data) {
  return request({
    url: `/project/contractInfo/dto`,
    method: 'put',
    data: data
  });
}

// 获取合同详情
export function getSignContractDetail(id) {
  return request({
    url: `/project/contractInfo/${id}`,
    method: 'get',
  });
}

// 1.4
// 查询付款项目合同信息列表
export function contractPaymentInfo(data) {
  return request({
    url: '/project/contractPaymentInfo/list',
    method: 'post',
    data: data
  });
}
export function invoicePaymentList(data) {
  return request({
    url: '/project/invoicePayment/getByInvoicingPayment',
    method: 'post',
    data:data,
  });
}
// 新增项目合同信息
export function addContractPaymentInfo(data) {
  return request({
    url: '/project/contractPaymentInfo/add',
    method: 'post',
    data: data
  });
}
// 修改项目合同信息
export function putContractPaymentInfo(data) {
  return request({
    url: '/project/contractPaymentInfo/update',
    method: 'put',
    data: data
  });
}
// 获取项目合同信息详细信息
export function getContractPaymentInfo(id) {
  return request({
    url: `/project/contractPaymentInfo/${id}`,
    method: 'get',
  });
}
// 付款申请信息展示
export function postInvoicePayment(id) {
  return request({
    url: `/project/invoicePayment/getByContractId/${id}`,
    method: 'get',
  });
}
// 新增付款申请
export function addInvoicePayment(data) {
  return request({
    url: '/project/invoicePayment/add',
    method: 'post',
    data: data
  });
}
// 修改付款申请
export function putInvoicePayment(data) {
  return request({
    url: '/project/invoicePayment/update',
    method: 'post',
    data: data
  });
}
// 代办--》付款申请回显信息展示

export function getById(id) {
  return request({
    url: `/project/invoicePayment/getById/${id}`,
    method: 'get',
  });
}

// 根据ID查询销售合同
export function getContractById(data) {
  return request({
    url: `/project/contractInfo/projectContractlist`,
    method: 'post',
    data:data
  });
}