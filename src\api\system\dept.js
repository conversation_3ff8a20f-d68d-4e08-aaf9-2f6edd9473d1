import request from '@/utils/request'

// 查询部门列表
export function listDept(query) {
    return request({
        url: '/system/organizational/tree-list',
        method: 'get',
        params: query
    })
}
// 部门下拉列表 部门列表 去除禁用部门
export function listDeptRemove(query) {
    return request({
        url: '/system/organizational/tree-list',
        method: 'get',
        params: {
            ...query,
            status:1
        }
    })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
    return request({
        url: '/system/organizational/list/exclude/' + deptId,
        method: 'get'
    })
}

// 查询部门详细
export function getDept(deptId) {
    return request({
        url: '/system/organizational/' + deptId,
        method: 'get'
    })
}

// 查询部门下拉树结构
export function treeselect(data) {
    return request({
        url: '/system/organizational/treeselect',
        method: 'post',
        data
    })
}
// 查询部门下拉树结构 新 带人数
export function treeselectNew(data) {
    return request({
        url: '/system/organizational/rosterOrgLect',
        method: 'get',
        data
    })
}
// 查询部门下拉树结构 新 离职
export function treeselectDepart(data) {
    return request({
        url: '/system/organizational/departOrgLect',
        method: 'get',
        data
    })
}
// 查询部门下拉树结构 新 退休
export function treeselectRetire(data) {
    return request({
        url: '/system/organizational/retireOrgLect',
        method: 'get',
        data
    })
}
// 查询部门下拉树结构 新 清理缓存
export function treeselectRefresh(data) {
    return request({
        url: '/system/organizational/initCache',
        method: 'get',
        data
    })
}

// 根据角色ID查询部门树结构
export function roleDeptTreeselect(roleId) {
    return request({
        url: '/system/organizational/roleDeptTreeselect/' + roleId,
        method: 'get'
    })
}

// 新增部门
export function addDept(data) {
    return request({
        url: '/system/organizational',
        method: 'post',
        data: data
    })
}

// 修改部门
export function updateDept(data) {
    return request({
        url: '/system/organizational',
        method: 'put',
        data: data
    })
}

// 删除部门
export function delDept(deptId) {
    return request({
        url: '/system/organizational/' + deptId,
        method: 'delete'
    })
}

// 调整排序/roster/sort
export function sort(data) {
  return request({
    url: '/system/organizational/updateOrgSort',
    method: 'put',
    data:data
  })
}

