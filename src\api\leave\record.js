import request from '@/utils/request'

//保存
export function addRecord(data) {
  return request({
    url: '/attendance/leaveRecord',
    method: 'post',
    data: data
  })
}

// 修改
export function editRecord(data) {
  return request({
    url: '/attendance/leaveRecord',
    method: 'put',
    data: data
  })
}

// 提交
export function submitRecord(data) {
  return request({
    url: '/attendance/leaveRecord/submit',
    method: 'post',
    data: data
  })
}

export function getRecordById(id) {
  return request({
    url: '/attendance/leaveRecord/' + id,
    method: 'get'
  })
}

export function getHistorySummy(userId) {
  return request({
    url: '/attendance/leaveRecordDetail/' + userId,
    method: 'get'
  })
}

export function listLeaveRecord(data){
  return request({
    url: '/attendance/leaveRecord/list',
    method: 'post',
    data
  })
}


