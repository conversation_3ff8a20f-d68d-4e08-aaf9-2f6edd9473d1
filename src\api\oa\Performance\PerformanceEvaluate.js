import request from '@/utils/request'

// 绩效考评列表
export function evaluationList(data) {
	return request({
	    url: '/hr/performanceEvaluation/projectManagerList',
	    method: 'post',
	    data: data
	})
}
// 人资绩效考评列表
export function hrEvaluationList(data) {
	return request({
	    url: '/hr/performanceEvaluation/hrUserEvaluationList',
	    method: 'post',
	    data: data
	})
}

// 获取审批绩效考评列表
export function approvalEvaluationList(data) {
	return request({
	    url: '/hr/performanceEvaluation/batchApproval',
	    method: 'post',
	    data: data
	})
}
// 绩效考核指南
export function getGuide(programmeId) {
	return request({
		url: `/hr/performanceEvaluation/nzhinan/${programmeId}`,
		method: 'get'
	});
}
// 绩效考核指南及详情
export function getEvaluationAndGuide({programmeId,evaluationId}) {
	return request({
		url: `/hr/performanceEvaluation/detail?programmeId=${programmeId}&evaluationId=${evaluationId}`,
		method: 'get'
	});
}
// 绩效考核详情
export function getEvaluation(id) {
	return request({
		url: `/hr/performanceEvaluation/${id}`,
		method: 'get'
	});
}
// 暂存绩效考评列表
export function saveEvaluationList(data) {
	return request({
	    url: '/hr/performanceEvaluation/temporaryEvaluation',
	    method: 'post',
	    data: data
	})
}
// 提交绩效考评列表
export function submitEvaluationList(data) {
	return request({
	    url: '/hr/performanceEvaluation/submitEvaluation',
	    method: 'post',
	    data: data
	})
}
// 绩效考评页面  事业部负责人提交绩效考评列表
export function leaderSubmitEvaluation(data) {
	return request({
	    url: '/hr/performanceEvaluation/leaderSubmitEvaluation',
	    method: 'post',
	    data: data
	})
}


// 绩效考评状态枚举
export function evaluationStatusData () {
	return request({
	    url: '/hr/performanceEvaluation/getAllStatusEnumMap',
	    method: 'get',
	})
}
// 待办已办跳转获取绩效详情(业务id获取详情)
export function getPerformanceEvaluation(id) {
	return request({
		url: `/hr/performanceEvaluation/getPerformanceEvaluationByAppealId?id=${id}`,
		method: 'get'
	});
}

// 查询个人绩效考评列表
export function getUserEvaluationList(data) {
	return request({
		url: `/hr/performanceEvaluation/userEvaluationList`,
		method: 'post',
		data
	});
}
