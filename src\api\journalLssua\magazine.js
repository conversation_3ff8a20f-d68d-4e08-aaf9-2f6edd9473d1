import request from '@/utils/request'

// 分页查询公文模板列表
export function listMagazine(query) {
  return request({
    url: '/manuscript/magazine/list',
    method: 'post',
    data: query
  })
}

// 新增公文模板
export function addMagazine(data) {
  return request({
    url: '/manuscript/magazine',
    method: 'post',
    data: data
  })
}

// 修改公文模板
export function editMagazine(data) {
  return request({
    url: '/manuscript/magazine',
    method: 'put',
    data: data
  })
}

// 删除公文模板
export function delMagazine(id) {
  return request({
    url: '/manuscript/magazine/' + id,
    method: 'delete'
  })
}

// 查询公文模板详细
export function getMagazine(id) {
  return request({
    url: '/manuscript/magazine/' + id,
    method: 'get'
  })
}


