import request from "@/utils/request";

// 回执状态数据
export function receiptStatusData() {
  return request({
    url: "/project/common/getReceiptStatusEnum",
    method: "get",
  });
}

// 收款状态数据
export function collectionStatusData() {
  return request({
    url: "/project/common/getCollectionStatusEnum",
    method: "get",
  });
}

// 开票列表数据
export function listTicket1(data) {
  return request({
    url: "/project/invoiceRequisition/list",
    method: "GET",
    params: data,
  });
}
export function listTicket(data) {
  return request({
    url:
      "/project/invoiceRequisition/getList?pageNum=" +
      data.pageNum +
      "&pageSize=" +
      data.pageSize,
    method: "post",
    data: data,
  });
}

//开票申请中获取合同编码
export function contractInfoData() {
  return request({
    url: "/project/contractInfo/list",
    method: "get",
  });
}

//开票申请中获取合同及客户数据
export function contractInfoDetailData(id) {
  return request({
    url: "/project/contractInfo/" + id,
    method: "get",
  });
}

//项目开票明细删除
export function ticketDetailDel(id) {
  return request({
    url: "/project/projectInfo/" + id,
    method: "delete",
  });
}

//获取项目信息下拉框数据
export function projectCodeInfoData() {
  return request({
    url: "/project/projectInfo/list",
    method: "post",
    data: {},
  });
}

//选择项目编码下拉框带出项目名称及经理
export function projectInfoData(id) {
  return request({
    url: "/project/projectInfo/" + id,
    method: "get",
  });
}

//新增开票申请
export function postRequisitionSubmit(data) {
  return request({
    url: "/project/invoiceRequisition",
    method: "post",
    data: data,
  });
}

//修改开票申请
export function putRequisitionSubmit(data) {
  return request({
    url: "/project/invoiceRequisition",
    method: "put",
    data: data,
  });
}

//获取开票信息
export function invoiceRequisitionDetailData(id) {
  return request({
    url: "/project/invoiceRequisition/" + id,
    method: "get",
  });
}

//获取开票回执列表
export function returnListData(id) {
  return request({
    url: "/project/invoiceReturn/list",
    method: "get",
    params: {
      id: id,
    },
  });
}

//项目开票回执删除
export function invoiceReturnDel(id) {
  return request({
    url: "/project/invoiceReturn/" + id,
    method: "delete",
  });
}

//开票回执新增
export function returnSubmit(data) {
  return request({
    url: "/project/invoiceReturn",
    method: "post",
    data: data,
  });
}

//开票回执修改
export function putReturnSubmit(data) {
  return request({
    url: "/project/invoiceReturn/updateReturn",
    method: "put",
    data: data,
  });
}

//获取收款明细列表
export function collectListData(id) {
  return request({
    url: "/project/invoiceReturn/list",
    method: "get",
    params: {
      id: id,
    },
  });
}

//收款明细删除
export function collectListDataDel(id) {
  return request({
    url: "/project/collectionDetail/" + id,
    method: "delete",
  });
}

//获取项目收款明细列表
export function projectcollectListData(id) {
  return request({
    url: "/project/invoiceCollectionDetail/" + id,
    method: "get",
  });
}

//项目收款明细删除
export function projectcollectDataDel(id) {
  return request({
    url: "/project/invoiceCollectionDetail/" + id,
    method: "delete",
  });
}

//收款获取开票详情
export function ticketInfoData(id) {
  return request({
    url: "/project/invoiceCollection/getData",
    method: "get",
    params: {
      invoicingRequisitionId: id,
    },
  });
}

//查询开票类型列表
export function invoiveTypeListData() {
  return request({
    url: "/project/invoiceType/list",
    method: "get",
  });
}

//根据开票类型查税率
export function taxRateData(id) {
  return request({
    url: "/project/invoiceType/" + id,
    method: "get",
  });
}

//票号下拉框数据
export function invoicingReturnIdArrData(id) {
  return request({
    url: "/project/invoiceReturn/list",
    method: "get",
    params: {
      id: id,
    },
  });
}

//收款申请提交
export function collectSubmit(data) {
  return request({
    url: "/project/invoiceCollection",
    method: "post",
    data: data,
  });
}

//根据合同id获取项目编码下拉框及数据
export function projectCodeInfoContractIdData(id) {
  return request({
    url: "/project/contractDetail/list/" + id,
    method: "get",
  });
}

//根据合同id获取项目编码下拉框及数据2222
export function postContractDetailList(data) {
  return request({
    url: "/project/contractDetail/list",
    method: "post",
    data: data,
  });
}

//获取开票类型
export function getRequisitionTypeEnumMap() {
  return request({
    url: "project/common/getRequisitionTypeEnumMap",
    method: "get",
  });
}
