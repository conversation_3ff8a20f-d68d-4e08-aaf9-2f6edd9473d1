import request from '@/utils/request'

// 查询知识库附件列表
export function listFile(query) {
  return request({
    url: '/oa/file/list',
    method: 'get',
    params: query
  })
}

// 查询知识库附件详细
export function getFile(id) {
  return request({
    url: '/oa/file/' + id,
    method: 'get'
  })
}

// 新增知识库附件
export function addFile(data) {
  return request({
    url: '/oa/file',
    method: 'post',
    data: data
  })
}






// 删除知识库附件
export function delFile(id) {
  return request({
    url: '/oa/file/' + id,
    method: 'delete'
  })
}

//全文搜素文件列表
export function selFileList(query) {
  return request({
    url: '/oa/file/selList',
    method: 'get',
    params: query
  })
}
